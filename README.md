# After-Hours Volume Alert Desktop App

A Python desktop application that monitors after-hours trading volume and sends email alerts when volume exceeds specified thresholds.

## 🚀 Features

- **Real-time Volume Monitoring**: Analyzes after-hours trading volume using Finnhub API
- **Smart Alerts**: Compares current volume against historical averages
- **Email Notifications**: Sends detailed HTML email alerts when thresholds are exceeded
- **User-friendly GUI**: Clean Tkinter interface for easy configuration and monitoring
- **Automated Scanning**: Can run scheduled scans without GUI interaction
- **Comprehensive Logging**: Detailed logs for troubleshooting and audit trails

## 📋 Requirements

- Python 3.9 or higher
- Internet connection for API access
- Email account with app password (Gmail recommended)
- Finnhub API key (free tier available)

## 🛠️ Installation

1. **Clone or download the project files**
2. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

## 🔧 Setup

### 1. Get Finnhub API Key
1. Visit [Finnhub.io](https://finnhub.io/)
2. Sign up for a free account
3. Get your API key from the dashboard

### 2. Configure Email (Gmail Example)
1. Enable 2-factor authentication on your Gmail account
2. Generate an App Password:
   - Go to Google Account settings
   - Security → 2-Step Verification → App passwords
   - Generate password for "Mail"
3. Use this app password (not your regular password) in the application

### 3. First Run
```bash
python main.py
```

## 📖 Usage

### GUI Mode (Default)

1. **Configuration Tab**:
   - Enter your Finnhub API key
   - Configure email settings (SMTP server, credentials)
   - Set volume threshold (e.g., 2.0 = 2x average volume)
   - Set days to average (e.g., 5 = last 5 trading days)
   - Test API and email connections
   - Save configuration

2. **Watchlist Tab**:
   - Add stock symbols (one per line)
   - Load/save watchlist files
   - Validate symbols

3. **Monitor Tab**:
   - Click "Scan Now" to analyze watchlist
   - View real-time progress and results
   - See detailed volume analysis

4. **Logs Tab**:
   - View application logs
   - Monitor scan history

### Automated Mode (Scheduled)

Run without GUI for scheduled tasks:
```bash
python main.py --auto
```

## 📊 How It Works

1. **Data Collection**: Fetches 5-minute interval data for after-hours periods (8:00 PM - 9:29 AM EST)
2. **Volume Calculation**: Sums total volume during after-hours window
3. **Historical Analysis**: Calculates average volume over specified number of days
4. **Alert Logic**: Triggers alert if current volume ≥ threshold × average volume
5. **Notification**: Sends detailed email with analysis results

## 📁 File Structure

```
volume_alert_app/
├── main.py              # Main application entry point
├── config_manager.py    # Configuration handling
├── volume_analyzer.py   # Finnhub API integration & analysis
├── email_notifier.py    # Email notification system
├── gui_components.py    # Tkinter GUI components
├── config.json          # Configuration file
├── stocks.txt           # Default watchlist
├── scan_log.txt         # Application logs
├── requirements.txt     # Python dependencies
└── README.md           # This file
```

## ⚙️ Configuration Options

### Analysis Settings
- **Volume Threshold**: Multiplier for average volume (default: 2.0x)
- **Days to Average**: Number of historical days to analyze (default: 5)
- **After-hours Window**: 8:00 PM - 9:29 AM EST

### Email Settings
- **SMTP Server**: Email server (default: smtp.gmail.com)
- **SMTP Port**: Server port (default: 587)
- **Sender Email**: Your email address
- **App Password**: Email app password (not regular password)
- **Recipient Email**: Alert destination email

## 🔄 Scheduling (Optional)

### Windows Task Scheduler
1. Open Task Scheduler
2. Create Basic Task
3. Set trigger (e.g., daily at 8:30 AM)
4. Set action: `pythonw.exe path\to\main.py --auto`

### Linux/macOS Cron
```bash
# Edit crontab
crontab -e

# Add line for 8:30 AM daily
30 8 * * * /usr/bin/python3 /path/to/main.py --auto
```

## 🚨 Important Notes

### API Rate Limits
- Finnhub free tier: 60 calls/minute
- App includes rate limiting delays
- Consider upgrading for high-frequency monitoring

### Market Hours
- After-hours definition: 8:00 PM - 9:29 AM EST
- Excludes weekends and holidays (basic implementation)
- For production use, implement proper market calendar

### Email Security
- Always use app passwords, never regular passwords
- Keep credentials secure
- Test email configuration before relying on alerts

## 🐛 Troubleshooting

### Common Issues

1. **API Connection Failed**
   - Verify API key is correct
   - Check internet connection
   - Ensure Finnhub account is active

2. **Email Not Sending**
   - Verify app password (not regular password)
   - Check SMTP settings
   - Ensure 2FA is enabled for Gmail

3. **No Volume Data**
   - Some stocks may not have after-hours data
   - Check if symbol is valid
   - Verify market is open/closed as expected

4. **High Memory Usage**
   - Large watchlists consume more memory
   - Consider splitting into smaller batches
   - Monitor system resources

### Log Files
Check `scan_log.txt` for detailed error messages and debugging information.

## 📈 Example Alert Email

```
🚨 After-Hours Volume Alert

Analysis Time: 2024-01-15 08:30:00
Alerts Triggered: 2
Total Analyzed: 10

🚨 TRIGGERED ALERTS:
AAPL: 3.2x (Current: 1,250,000, Avg: 390,625)
TSLA: 2.8x (Current: 890,000, Avg: 317,857)

📊 ALL STOCKS:
🚨 AAPL: 3.2x
✅ MSFT: 1.1x
✅ GOOGL: 0.8x
🚨 TSLA: 2.8x
✅ AMZN: 1.4x
```

## 🔮 Future Enhancements

- [ ] Market calendar integration
- [ ] Multiple alert thresholds
- [ ] Historical performance tracking
- [ ] Sound alerts for GUI mode
- [ ] CSV export functionality
- [ ] Advanced charting with Matplotlib
- [ ] Slack/Discord notifications
- [ ] Database storage for historical data

## 📄 License

This project is provided as-is for educational and personal use.

## 🤝 Support

For issues and questions:
1. Check the troubleshooting section
2. Review log files for error details
3. Verify configuration settings
4. Test individual components (API, email) separately
