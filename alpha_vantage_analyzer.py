"""
Alpha Vantage Volume Analyzer Module
===================================

Handles after-hours volume analysis using Alpha Vantage API.
Supports extended hours data on free tier.
"""

import requests
import logging
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import json

class AlphaVantageAnalyzer:
    """Analyzes after-hours volume using Alpha Vantage API."""
    
    def __init__(self, config_manager):
        """
        Initialize Alpha Vantage analyzer.
        
        Args:
            config_manager: Configuration manager instance
        """
        self.config = config_manager
        self.logger = logging.getLogger(__name__)
        self.session = requests.Session()
        
        # Set up session headers
        self.session.headers.update({
            'User-Agent': 'VolumeAlertApp/1.0'
        })
    
    def _get_api_key(self) -> str:
        """
        Get Alpha Vantage API key from configuration.
        
        Returns:
            str: API key
            
        Raises:
            ValueError: If API key is not configured
        """
        api_key = self.config.get('alpha_vantage.api_key')
        if not api_key:
            raise ValueError("Alpha Vantage API key not configured")
        return api_key
    
    def _make_api_request(self, function: str, params: Dict) -> Optional[Dict]:
        """
        Make API request to Alpha Vantage.
        
        Args:
            function (str): API function name
            params (Dict): Request parameters
            
        Returns:
            Optional[Dict]: API response data or None if failed
        """
        try:
            # Add API key and function to parameters
            params['apikey'] = self._get_api_key()
            params['function'] = function
            
            url = "https://www.alphavantage.co/query"
            
            self.logger.debug(f"Making API request: {function} with params: {params}")
            
            response = self.session.get(url, params=params, timeout=30)
            response.raise_for_status()
            
            data = response.json()
            
            # Check for API errors
            if 'Error Message' in data:
                self.logger.error(f"API error: {data['Error Message']}")
                return None
            
            if 'Note' in data:
                self.logger.warning(f"API note: {data['Note']}")
                return None
            
            return data
            
        except requests.exceptions.RequestException as e:
            self.logger.error(f"API request failed: {e}")
            return None
        except json.JSONDecodeError as e:
            self.logger.error(f"Failed to parse API response: {e}")
            return None
    
    def test_api_connection(self) -> Tuple[bool, str]:
        """
        Test API connection and key validity.
        
        Returns:
            Tuple[bool, str]: (success, message)
        """
        try:
            # Test with a simple quote request
            data = self._make_api_request('GLOBAL_QUOTE', {'symbol': 'AAPL'})
            
            if data is None:
                return False, "Failed to connect to Alpha Vantage API"
            
            if 'Global Quote' not in data:
                return False, "Invalid API response format"
            
            # Test intraday with extended hours
            intraday_data = self._make_api_request('TIME_SERIES_INTRADAY', {
                'symbol': 'AAPL',
                'interval': '5min',
                'extended_hours': 'true',
                'outputsize': 'compact'
            })
            
            extended_msg = ""
            if intraday_data and 'Time Series (5min)' in intraday_data:
                extended_msg = " (Extended hours: Available)"
            else:
                extended_msg = " (Extended hours: Limited/Not available)"
            
            return True, f"API connection successful{extended_msg}"
            
        except Exception as e:
            return False, f"API test failed: {e}"
    
    def _get_trading_day(self, days_back: int = 0) -> datetime:
        """
        Get a specific trading day.
        
        Args:
            days_back (int): Number of trading days back (0 = most recent)
            
        Returns:
            datetime: Trading day
        """
        # Start from last Friday (May 23, 2025) since Monday was Memorial Day
        if days_back == 0:
            return datetime(2025, 5, 23)  # Most recent trading day
        else:
            # Go back from Friday May 23, 2025
            current_date = datetime(2025, 5, 23)
            trading_days_found = 0
            
            while trading_days_found < days_back:
                current_date -= timedelta(days=1)
                # Check if it's a weekday (Monday=0, Sunday=6)
                if current_date.weekday() < 5:  # Monday to Friday
                    trading_days_found += 1
            
            return current_date
    
    def get_after_hours_volume(self, symbol: str, days_back: int = 0) -> Optional[int]:
        """
        Get after-hours volume for a specific stock and day.
        
        Args:
            symbol (str): Stock symbol
            days_back (int): Number of trading days back
            
        Returns:
            Optional[int]: After-hours volume or None if failed
        """
        try:
            target_date = self._get_trading_day(days_back)
            
            # Get intraday data with extended hours
            params = {
                'symbol': symbol.upper(),
                'interval': '5min',
                'extended_hours': 'true',
                'outputsize': 'full',
                'month': target_date.strftime('%Y-%m')  # Get specific month
            }
            
            data = self._make_api_request('TIME_SERIES_INTRADAY', params)
            
            if data is None or 'Time Series (5min)' not in data:
                self.logger.warning(f"No intraday data available for {symbol}")
                return None
            
            time_series = data['Time Series (5min)']
            
            # Filter for after-hours times on the target date
            target_date_str = target_date.strftime('%Y-%m-%d')
            after_hours_volume = 0
            
            for timestamp, values in time_series.items():
                if target_date_str in timestamp:
                    # Parse time to check if it's after hours
                    dt = datetime.strptime(timestamp, '%Y-%m-%d %H:%M:%S')
                    hour = dt.hour
                    
                    # After hours: before 9:30 AM or after 4:00 PM
                    if hour < 9 or (hour == 9 and dt.minute < 30) or hour >= 16:
                        volume = int(values['5. volume'])
                        after_hours_volume += volume
            
            self.logger.info(f"{symbol} after-hours volume (day -{days_back}): {after_hours_volume:,}")
            return after_hours_volume
            
        except Exception as e:
            self.logger.error(f"Error getting after-hours volume for {symbol}: {e}")
            return None
    
    def calculate_average_volume(self, symbol: str, days: int) -> Optional[float]:
        """
        Calculate average after-hours volume over specified days.
        
        Args:
            symbol (str): Stock symbol
            days (int): Number of days to average over
            
        Returns:
            Optional[float]: Average volume or None if insufficient data
        """
        volumes = []
        
        for day_back in range(1, days + 1):  # Start from 1 day back
            volume = self.get_after_hours_volume(symbol, day_back)
            if volume is not None:
                volumes.append(volume)
            
            # Add delay to avoid rate limiting (Alpha Vantage: 5 calls/minute free)
            time.sleep(12)  # 12 seconds between calls = 5 calls/minute
        
        if not volumes:
            self.logger.warning(f"No volume data available for {symbol}")
            return None
        
        if len(volumes) < days * 0.6:  # Require at least 60% of requested days
            self.logger.warning(f"Insufficient data for {symbol}: {len(volumes)}/{days} days")
            return None
        
        average = sum(volumes) / len(volumes)
        self.logger.debug(f"{symbol} average after-hours volume over {len(volumes)} days: {average:,.0f}")
        
        return average
    
    def analyze_stock(self, symbol: str) -> Optional[Dict]:
        """
        Analyze a single stock for after-hours volume alerts.
        
        Args:
            symbol (str): Stock symbol to analyze
            
        Returns:
            Optional[Dict]: Analysis results or None if failed
        """
        try:
            # Get configuration
            threshold = self.config.get('analysis.volume_threshold', 2.0)
            days_to_average = self.config.get('analysis.days_to_average', 5)
            
            # Get today's after-hours volume
            current_volume = self.get_after_hours_volume(symbol, 0)
            if current_volume is None:
                return None
            
            # Get average volume
            avg_volume = self.calculate_average_volume(symbol, days_to_average)
            if avg_volume is None:
                return None
            
            # Calculate ratio
            volume_ratio = current_volume / avg_volume if avg_volume > 0 else 0
            
            # Determine if alert should be triggered
            alert_triggered = volume_ratio >= threshold
            
            result = {
                'symbol': symbol.upper(),
                'current_volume': current_volume,
                'average_volume': avg_volume,
                'volume_ratio': volume_ratio,
                'threshold': threshold,
                'alert_triggered': alert_triggered,
                'analysis_time': datetime.now().isoformat()
            }
            
            self.logger.info(f"{symbol}: After-hours volume ratio {volume_ratio:.2f}x "
                           f"(current: {current_volume:,}, avg: {avg_volume:,.0f}) "
                           f"- Alert: {alert_triggered}")
            
            return result
            
        except Exception as e:
            self.logger.error(f"Error analyzing {symbol}: {e}")
            return None
    
    def analyze_watchlist(self, symbols: List[str], 
                         progress_callback: Optional[callable] = None) -> Dict:
        """
        Analyze multiple stocks from watchlist.
        
        Args:
            symbols (List[str]): List of stock symbols
            progress_callback (Optional[callable]): Progress update callback
            
        Returns:
            Dict: Analysis results with alerts and summary
        """
        results = {
            'alerts': [],
            'analyzed': [],
            'failed': [],
            'summary': {
                'total_symbols': len(symbols),
                'successful_analyses': 0,
                'alerts_triggered': 0,
                'analysis_time': datetime.now().isoformat()
            }
        }
        
        for i, symbol in enumerate(symbols):
            try:
                # Update progress
                if progress_callback:
                    progress = (i / len(symbols)) * 100
                    progress_callback(progress, f"Analyzing {symbol}...")
                
                # Analyze stock
                analysis = self.analyze_stock(symbol.strip().upper())
                
                if analysis:
                    results['analyzed'].append(analysis)
                    results['summary']['successful_analyses'] += 1
                    
                    if analysis['alert_triggered']:
                        results['alerts'].append(analysis)
                        results['summary']['alerts_triggered'] += 1
                else:
                    results['failed'].append(symbol)
                
            except Exception as e:
                self.logger.error(f"Error in watchlist analysis for {symbol}: {e}")
                results['failed'].append(symbol)
        
        # Final progress update
        if progress_callback:
            progress_callback(100, "Analysis complete")
        
        self.logger.info(f"Watchlist analysis complete: "
                        f"{results['summary']['successful_analyses']}/{len(symbols)} analyzed, "
                        f"{results['summary']['alerts_triggered']} alerts")
        
        return results
