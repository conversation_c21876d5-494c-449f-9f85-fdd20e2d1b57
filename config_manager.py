"""
Configuration Manager Module
============================

Handles loading, saving, and validation of application configuration.
Provides fallback defaults and input validation.
"""

import json
import os
import logging
from typing import Dict, Any, Optional, List
import re

class ConfigManager:
    """Manages application configuration with validation and defaults."""

    def __init__(self, config_file='config.json'):
        """
        Initialize configuration manager.

        Args:
            config_file (str): Path to configuration file
        """
        self.config_file = config_file
        self.logger = logging.getLogger(__name__)
        self.config = self._load_config()

    def _get_default_config(self) -> Dict[str, Any]:
        """
        Get default configuration values.

        Returns:
            Dict: Default configuration
        """
        return {
            'finnhub': {
                'api_key': '',
                'base_url': 'https://finnhub.io/api/v1'
            },
            'email': {
                'smtp_server': 'smtp.gmail.com',
                'smtp_port': 587,
                'sender_email': '',
                'sender_password': '',  # App password for Gmail
                'recipient_email': ''
            },
            'analysis': {
                'volume_threshold': 2.0,  # 2x average volume
                'days_to_average': 5,     # Last 5 trading days
                'after_hours_start': '20:00',  # 8:00 PM EST
                'after_hours_end': '09:29'      # 9:29 AM EST
            },
            'watchlist': {
                'stocks_file': 'stocks.txt',
                'default_stocks': ['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA']
            },
            'logging': {
                'log_file': 'scan_log.txt',
                'log_level': 'INFO'
            }
        }

    def _load_config(self) -> Dict[str, Any]:
        """
        Load configuration from file with fallback to defaults.

        Returns:
            Dict: Loaded configuration
        """
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)

                # Merge with defaults to ensure all keys exist
                default_config = self._get_default_config()
                merged_config = self._merge_configs(default_config, config)

                self.logger.info(f"Configuration loaded from {self.config_file}")
                return merged_config
            else:
                self.logger.info("Config file not found, using defaults")
                return self._get_default_config()

        except (json.JSONDecodeError, IOError) as e:
            self.logger.error(f"Error loading config: {e}")
            self.logger.info("Using default configuration")
            return self._get_default_config()

    def _merge_configs(self, default: Dict, user: Dict) -> Dict:
        """
        Recursively merge user config with defaults.

        Args:
            default (Dict): Default configuration
            user (Dict): User configuration

        Returns:
            Dict: Merged configuration
        """
        result = default.copy()

        for key, value in user.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._merge_configs(result[key], value)
            else:
                result[key] = value

        return result

    def save_config(self) -> bool:
        """
        Save current configuration to file.

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=4, ensure_ascii=False)

            self.logger.info(f"Configuration saved to {self.config_file}")
            return True

        except IOError as e:
            self.logger.error(f"Error saving config: {e}")
            return False

    def get(self, key_path: str, default=None) -> Any:
        """
        Get configuration value using dot notation.

        Args:
            key_path (str): Dot-separated path (e.g., 'email.smtp_server')
            default: Default value if key not found

        Returns:
            Any: Configuration value
        """
        keys = key_path.split('.')
        value = self.config

        try:
            for key in keys:
                value = value[key]
            return value
        except (KeyError, TypeError):
            return default

    def set(self, key_path: str, value: Any) -> None:
        """
        Set configuration value using dot notation.

        Args:
            key_path (str): Dot-separated path
            value (Any): Value to set
        """
        keys = key_path.split('.')
        config = self.config

        # Navigate to parent of target key
        for key in keys[:-1]:
            if key not in config:
                config[key] = {}
            config = config[key]

        # Set the value
        config[keys[-1]] = value

    def validate_email(self, email: str) -> bool:
        """
        Validate email address format.

        Args:
            email (str): Email address to validate

        Returns:
            bool: True if valid, False otherwise
        """
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return bool(re.match(pattern, email))

    def validate_threshold(self, threshold: float) -> bool:
        """
        Validate volume threshold value.

        Args:
            threshold (float): Threshold to validate

        Returns:
            bool: True if valid, False otherwise
        """
        return isinstance(threshold, (int, float)) and threshold > 0

    def validate_days(self, days: int) -> bool:
        """
        Validate days to average value.

        Args:
            days (int): Days to validate

        Returns:
            bool: True if valid, False otherwise
        """
        return isinstance(days, int) and 1 <= days <= 30

    def is_configured(self) -> bool:
        """
        Check if essential configuration is present.

        Returns:
            bool: True if configured, False otherwise
        """
        required_fields = [
            'alpha_vantage.api_key',
            'email.sender_email',
            'email.sender_password',
            'email.recipient_email'
        ]

        for field in required_fields:
            value = self.get(field)
            if not value or (isinstance(value, str) and not value.strip()):
                return False

        return True

    def get_missing_config(self) -> List[str]:
        """
        Get list of missing required configuration fields.

        Returns:
            List[str]: List of missing field names
        """
        required_fields = {
            'alpha_vantage.api_key': 'Alpha Vantage API Key',
            'email.sender_email': 'Sender Email',
            'email.sender_password': 'Email App Password',
            'email.recipient_email': 'Recipient Email'
        }

        missing = []
        for field, name in required_fields.items():
            value = self.get(field)
            if not value or (isinstance(value, str) and not value.strip()):
                missing.append(name)

        return missing
