"""
Email Notifier Module
====================

Handles email notifications for volume alerts.
Supports SMTP with TLS for secure email sending.
"""

import smtplib
import logging
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON><PERSON><PERSON><PERSON><PERSON>
from datetime import datetime
from typing import Dict, List, Tuple, Optional

class EmailNotifier:
    """Handles email notifications for volume alerts."""
    
    def __init__(self, config_manager):
        """
        Initialize email notifier.
        
        Args:
            config_manager: Configuration manager instance
        """
        self.config = config_manager
        self.logger = logging.getLogger(__name__)
    
    def _get_email_config(self) -> Dict[str, str]:
        """
        Get email configuration from config manager.
        
        Returns:
            Dict[str, str]: Email configuration
            
        Raises:
            ValueError: If required email configuration is missing
        """
        required_fields = {
            'smtp_server': self.config.get('email.smtp_server'),
            'smtp_port': self.config.get('email.smtp_port'),
            'sender_email': self.config.get('email.sender_email'),
            'sender_password': self.config.get('email.sender_password'),
            'recipient_email': self.config.get('email.recipient_email')
        }
        
        # Validate required fields
        missing_fields = [field for field, value in required_fields.items() 
                         if not value or (isinstance(value, str) and not value.strip())]
        
        if missing_fields:
            raise ValueError(f"Missing email configuration: {', '.join(missing_fields)}")
        
        return required_fields
    
    def test_email_connection(self) -> Tuple[bool, str]:
        """
        Test email server connection and authentication.
        
        Returns:
            Tuple[bool, str]: (success, message)
        """
        try:
            email_config = self._get_email_config()
            
            # Create SMTP connection
            server = smtplib.SMTP(email_config['smtp_server'], email_config['smtp_port'])
            server.starttls()  # Enable TLS encryption
            
            # Test authentication
            server.login(email_config['sender_email'], email_config['sender_password'])
            
            # Close connection
            server.quit()
            
            self.logger.info("Email connection test successful")
            return True, "Email connection successful"
            
        except smtplib.SMTPAuthenticationError:
            error_msg = "Email authentication failed. Check email and app password."
            self.logger.error(error_msg)
            return False, error_msg
            
        except smtplib.SMTPConnectError:
            error_msg = "Failed to connect to email server. Check server and port."
            self.logger.error(error_msg)
            return False, error_msg
            
        except Exception as e:
            error_msg = f"Email test failed: {e}"
            self.logger.error(error_msg)
            return False, error_msg
    
    def _create_alert_email(self, analysis_results: Dict) -> MIMEMultipart:
        """
        Create email message for volume alerts.
        
        Args:
            analysis_results (Dict): Results from volume analysis
            
        Returns:
            MIMEMultipart: Email message
        """
        email_config = self._get_email_config()
        
        # Create message
        msg = MIMEMultipart('alternative')
        msg['Subject'] = f"Volume Alert - {len(analysis_results['alerts'])} Stock(s) Triggered"
        msg['From'] = email_config['sender_email']
        msg['To'] = email_config['recipient_email']
        
        # Create email content
        text_content = self._create_text_content(analysis_results)
        html_content = self._create_html_content(analysis_results)
        
        # Attach both text and HTML versions
        text_part = MIMEText(text_content, 'plain')
        html_part = MIMEText(html_content, 'html')
        
        msg.attach(text_part)
        msg.attach(html_part)
        
        return msg
    
    def _create_text_content(self, results: Dict) -> str:
        """
        Create plain text email content.
        
        Args:
            results (Dict): Analysis results
            
        Returns:
            str: Plain text email content
        """
        lines = [
            "AFTER-HOURS VOLUME ALERT",
            "=" * 30,
            "",
            f"Analysis Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            f"Alerts Triggered: {len(results['alerts'])}",
            f"Total Analyzed: {results['summary']['successful_analyses']}",
            "",
            "TRIGGERED ALERTS:",
            "-" * 20
        ]
        
        # Add alert details
        for alert in results['alerts']:
            lines.extend([
                f"Symbol: {alert['symbol']}",
                f"Current Volume: {alert['current_volume']:,}",
                f"Average Volume: {alert['average_volume']:,.0f}",
                f"Volume Ratio: {alert['volume_ratio']:.2f}x",
                f"Threshold: {alert['threshold']:.1f}x",
                ""
            ])
        
        # Add summary of all analyzed stocks
        if results['analyzed']:
            lines.extend([
                "",
                "ALL ANALYZED STOCKS:",
                "-" * 20
            ])
            
            for stock in results['analyzed']:
                status = "🚨 ALERT" if stock['alert_triggered'] else "✅ Normal"
                lines.append(f"{stock['symbol']}: {stock['volume_ratio']:.2f}x - {status}")
        
        # Add failed stocks if any
        if results['failed']:
            lines.extend([
                "",
                "FAILED TO ANALYZE:",
                "-" * 20
            ])
            lines.extend(results['failed'])
        
        lines.extend([
            "",
            "---",
            "Volume Alert App",
            "Generated automatically"
        ])
        
        return "\n".join(lines)
    
    def _create_html_content(self, results: Dict) -> str:
        """
        Create HTML email content.
        
        Args:
            results (Dict): Analysis results
            
        Returns:
            str: HTML email content
        """
        html = f"""
        <html>
        <head>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                .header {{ background-color: #f44336; color: white; padding: 15px; border-radius: 5px; }}
                .summary {{ background-color: #f9f9f9; padding: 10px; margin: 10px 0; border-radius: 5px; }}
                .alert-table {{ width: 100%; border-collapse: collapse; margin: 10px 0; }}
                .alert-table th, .alert-table td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
                .alert-table th {{ background-color: #f2f2f2; }}
                .alert-row {{ background-color: #ffebee; }}
                .normal-row {{ background-color: #e8f5e8; }}
                .footer {{ margin-top: 20px; font-size: 12px; color: #666; }}
            </style>
        </head>
        <body>
            <div class="header">
                <h2>🚨 After-Hours Volume Alert</h2>
            </div>
            
            <div class="summary">
                <strong>Analysis Summary:</strong><br>
                Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}<br>
                Alerts Triggered: <strong>{len(results['alerts'])}</strong><br>
                Total Analyzed: {results['summary']['successful_analyses']}<br>
            </div>
        """
        
        if results['alerts']:
            html += """
            <h3>🚨 Triggered Alerts</h3>
            <table class="alert-table">
                <tr>
                    <th>Symbol</th>
                    <th>Current Volume</th>
                    <th>Average Volume</th>
                    <th>Ratio</th>
                    <th>Threshold</th>
                </tr>
            """
            
            for alert in results['alerts']:
                html += f"""
                <tr class="alert-row">
                    <td><strong>{alert['symbol']}</strong></td>
                    <td>{alert['current_volume']:,}</td>
                    <td>{alert['average_volume']:,.0f}</td>
                    <td><strong>{alert['volume_ratio']:.2f}x</strong></td>
                    <td>{alert['threshold']:.1f}x</td>
                </tr>
                """
            
            html += "</table>"
        
        # Add all analyzed stocks
        if results['analyzed']:
            html += """
            <h3>📊 All Analyzed Stocks</h3>
            <table class="alert-table">
                <tr>
                    <th>Symbol</th>
                    <th>Volume Ratio</th>
                    <th>Status</th>
                </tr>
            """
            
            for stock in results['analyzed']:
                row_class = "alert-row" if stock['alert_triggered'] else "normal-row"
                status = "🚨 ALERT" if stock['alert_triggered'] else "✅ Normal"
                
                html += f"""
                <tr class="{row_class}">
                    <td>{stock['symbol']}</td>
                    <td>{stock['volume_ratio']:.2f}x</td>
                    <td>{status}</td>
                </tr>
                """
            
            html += "</table>"
        
        # Add failed stocks if any
        if results['failed']:
            html += f"""
            <h3>❌ Failed to Analyze</h3>
            <p>{', '.join(results['failed'])}</p>
            """
        
        html += """
            <div class="footer">
                <hr>
                <p>Volume Alert App - Generated automatically</p>
            </div>
        </body>
        </html>
        """
        
        return html
    
    def send_alert_email(self, analysis_results: Dict) -> Tuple[bool, str]:
        """
        Send volume alert email.
        
        Args:
            analysis_results (Dict): Results from volume analysis
            
        Returns:
            Tuple[bool, str]: (success, message)
        """
        try:
            # Check if there are any alerts to send
            if not analysis_results.get('alerts'):
                return True, "No alerts to send"
            
            email_config = self._get_email_config()
            
            # Create email message
            msg = self._create_alert_email(analysis_results)
            
            # Send email
            server = smtplib.SMTP(email_config['smtp_server'], email_config['smtp_port'])
            server.starttls()
            server.login(email_config['sender_email'], email_config['sender_password'])
            
            text = msg.as_string()
            server.sendmail(email_config['sender_email'], email_config['recipient_email'], text)
            server.quit()
            
            success_msg = f"Alert email sent successfully to {email_config['recipient_email']}"
            self.logger.info(success_msg)
            return True, success_msg
            
        except Exception as e:
            error_msg = f"Failed to send alert email: {e}"
            self.logger.error(error_msg)
            return False, error_msg
    
    def send_test_email(self) -> Tuple[bool, str]:
        """
        Send a test email to verify configuration.
        
        Returns:
            Tuple[bool, str]: (success, message)
        """
        try:
            email_config = self._get_email_config()
            
            # Create test message
            msg = MIMEMultipart()
            msg['Subject'] = "Volume Alert App - Test Email"
            msg['From'] = email_config['sender_email']
            msg['To'] = email_config['recipient_email']
            
            body = f"""
            This is a test email from the Volume Alert App.
            
            Configuration Test Results:
            ✅ SMTP Server: {email_config['smtp_server']}:{email_config['smtp_port']}
            ✅ Sender Email: {email_config['sender_email']}
            ✅ Recipient Email: {email_config['recipient_email']}
            ✅ Authentication: Successful
            
            Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
            
            If you received this email, your configuration is working correctly!
            
            ---
            Volume Alert App
            """
            
            msg.attach(MIMEText(body, 'plain'))
            
            # Send email
            server = smtplib.SMTP(email_config['smtp_server'], email_config['smtp_port'])
            server.starttls()
            server.login(email_config['sender_email'], email_config['sender_password'])
            
            text = msg.as_string()
            server.sendmail(email_config['sender_email'], email_config['recipient_email'], text)
            server.quit()
            
            success_msg = f"Test email sent successfully to {email_config['recipient_email']}"
            self.logger.info(success_msg)
            return True, success_msg
            
        except Exception as e:
            error_msg = f"Failed to send test email: {e}"
            self.logger.error(error_msg)
            return False, error_msg
