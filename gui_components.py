"""
GUI Components Module
====================

Tkinter-based graphical user interface for the Volume Alert App.
Provides clean, organized interface for configuration and monitoring.
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext, filedialog
import threading
import logging
import os
from typing import Dict, Any, Callable

class VolumeAlertGUI:
    """Main GUI class for the Volume Alert Application."""

    def __init__(self, root: tk.Tk, app_instance):
        """
        Initialize the GUI.

        Args:
            root (tk.Tk): Root Tkinter window
            app_instance: Reference to main application instance
        """
        self.root = root
        self.app = app_instance
        self.logger = logging.getLogger(__name__)

        # Configure main window
        self.root.title("After-Hours Volume Alert")
        self.root.geometry("800x700")
        self.root.resizable(True, True)

        # Create GUI components
        self._create_widgets()
        self._load_current_config()

        # Bind window close event
        self.root.protocol("WM_DELETE_WINDOW", self._on_closing)

    def _create_widgets(self):
        """Create and layout all GUI widgets."""
        # Create notebook for tabs
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill='both', expand=True, padx=10, pady=10)

        # Create tabs
        self._create_config_tab()
        self._create_watchlist_tab()
        self._create_monitor_tab()
        self._create_log_tab()

    def _create_config_tab(self):
        """Create configuration tab."""
        config_frame = ttk.Frame(self.notebook)
        self.notebook.add(config_frame, text="Configuration")

        # Create scrollable frame
        canvas = tk.Canvas(config_frame)
        scrollbar = ttk.Scrollbar(config_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # API Configuration Section (Optional)
        api_group = ttk.LabelFrame(scrollable_frame, text="API Configuration (OPTIONAL)", padding=10)
        api_group.pack(fill='x', padx=10, pady=5)

        # No API required message
        ttk.Label(api_group, text="✅ NO API KEY REQUIRED!",
                 font=('TkDefaultFont', 10, 'bold')).grid(row=0, column=0, columnspan=3, sticky='w', pady=2)
        ttk.Label(api_group, text="The app now uses pure MarketWatch scraping - no API needed!").grid(
            row=1, column=0, columnspan=3, sticky='w', pady=2)

        ttk.Label(api_group, text="Alpha Vantage API Key (Optional):").grid(row=2, column=0, sticky='w', pady=(10, 2))
        self.api_key_var = tk.StringVar()
        api_entry = ttk.Entry(api_group, textvariable=self.api_key_var, width=50, show='*')
        api_entry.grid(row=2, column=1, sticky='ew', padx=(10, 0), pady=(10, 2))

        ttk.Button(api_group, text="Test Screener", command=self._test_api).grid(
            row=2, column=2, padx=(10, 0), pady=(10, 2))

        # Add link to get free API key
        ttk.Label(api_group, text="Get free API key at: https://www.alphavantage.co/support/#api-key").grid(
            row=3, column=0, columnspan=3, sticky='w', pady=(5, 0))

        api_group.columnconfigure(1, weight=1)

        # Email Configuration Section
        email_group = ttk.LabelFrame(scrollable_frame, text="Email Configuration", padding=10)
        email_group.pack(fill='x', padx=10, pady=5)

        # Email fields
        email_fields = [
            ("SMTP Server:", "smtp_server_var", "smtp.gmail.com"),
            ("SMTP Port:", "smtp_port_var", "587"),
            ("Sender Email:", "sender_email_var", ""),
            ("App Password:", "sender_password_var", ""),
            ("Recipient Email:", "recipient_email_var", "")
        ]

        self.email_vars = {}
        for i, (label, var_name, default) in enumerate(email_fields):
            ttk.Label(email_group, text=label).grid(row=i, column=0, sticky='w', pady=2)

            var = tk.StringVar(value=default)
            self.email_vars[var_name] = var

            show = '*' if 'password' in var_name.lower() else None
            entry = ttk.Entry(email_group, textvariable=var, width=40, show=show)
            entry.grid(row=i, column=1, sticky='ew', padx=(10, 0), pady=2)

        ttk.Button(email_group, text="Test Email", command=self._test_email).grid(
            row=len(email_fields), column=1, sticky='e', pady=(10, 0))

        email_group.columnconfigure(1, weight=1)

        # Analysis Configuration Section
        analysis_group = ttk.LabelFrame(scrollable_frame, text="Analysis Settings", padding=10)
        analysis_group.pack(fill='x', padx=10, pady=5)

        ttk.Label(analysis_group, text="Volume Threshold (x average):").grid(
            row=0, column=0, sticky='w', pady=2)
        self.threshold_var = tk.DoubleVar(value=2.0)
        threshold_spin = ttk.Spinbox(analysis_group, from_=1.0, to=10.0, increment=0.1,
                                   textvariable=self.threshold_var, width=10)
        threshold_spin.grid(row=0, column=1, sticky='w', padx=(10, 0), pady=2)

        ttk.Label(analysis_group, text="Days to Average:").grid(
            row=1, column=0, sticky='w', pady=2)
        self.days_var = tk.IntVar(value=5)
        days_spin = ttk.Spinbox(analysis_group, from_=1, to=30, increment=1,
                              textvariable=self.days_var, width=10)
        days_spin.grid(row=1, column=1, sticky='w', padx=(10, 0), pady=2)

        # Save Configuration Button
        save_frame = ttk.Frame(scrollable_frame)
        save_frame.pack(fill='x', padx=10, pady=10)

        ttk.Button(save_frame, text="Save Configuration",
                  command=self._save_config).pack(side='right')
        ttk.Button(save_frame, text="Load Defaults",
                  command=self._load_defaults).pack(side='right', padx=(0, 10))

        # Pack canvas and scrollbar
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

    def _create_watchlist_tab(self):
        """Create watchlist management tab."""
        watchlist_frame = ttk.Frame(self.notebook)
        self.notebook.add(watchlist_frame, text="Watchlist")

        # Instructions
        instructions = ttk.Label(watchlist_frame,
                               text="Enter stock symbols (one per line). Examples: AAPL, MSFT, GOOGL")
        instructions.pack(pady=10)

        # Watchlist text area
        text_frame = ttk.Frame(watchlist_frame)
        text_frame.pack(fill='both', expand=True, padx=10, pady=5)

        self.watchlist_text = scrolledtext.ScrolledText(text_frame, height=15, width=50)
        self.watchlist_text.pack(fill='both', expand=True)

        # Buttons frame
        button_frame = ttk.Frame(watchlist_frame)
        button_frame.pack(fill='x', padx=10, pady=10)

        ttk.Button(button_frame, text="Load from File",
                  command=self._load_watchlist_file).pack(side='left')
        ttk.Button(button_frame, text="Save to File",
                  command=self._save_watchlist_file).pack(side='left', padx=(10, 0))
        ttk.Button(button_frame, text="Validate Symbols",
                  command=self._validate_symbols).pack(side='right')

    def _create_monitor_tab(self):
        """Create monitoring and scanning tab."""
        monitor_frame = ttk.Frame(self.notebook)
        self.notebook.add(monitor_frame, text="Monitor")

        # Status section
        status_group = ttk.LabelFrame(monitor_frame, text="Status", padding=10)
        status_group.pack(fill='x', padx=10, pady=10)

        self.status_var = tk.StringVar(value="Ready to scan")
        status_label = ttk.Label(status_group, textvariable=self.status_var)
        status_label.pack()

        # Control buttons
        control_frame = ttk.Frame(monitor_frame)
        control_frame.pack(fill='x', padx=10, pady=5)

        self.scan_button = ttk.Button(control_frame, text="Scan Now",
                                    command=self._start_scan)
        self.scan_button.pack(side='left')

        self.stop_button = ttk.Button(control_frame, text="Stop Scan",
                                    command=self._stop_scan, state='disabled')
        self.stop_button.pack(side='left', padx=(10, 0))

        # Progress bar
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(monitor_frame, variable=self.progress_var,
                                          maximum=100)
        self.progress_bar.pack(fill='x', padx=10, pady=5)

        # Results area
        results_group = ttk.LabelFrame(monitor_frame, text="Scan Results", padding=10)
        results_group.pack(fill='both', expand=True, padx=10, pady=10)

        self.results_text = scrolledtext.ScrolledText(results_group, height=10)
        self.results_text.pack(fill='both', expand=True)

    def _create_log_tab(self):
        """Create log viewing tab."""
        log_frame = ttk.Frame(self.notebook)
        self.notebook.add(log_frame, text="Logs")

        # Log controls
        log_controls = ttk.Frame(log_frame)
        log_controls.pack(fill='x', padx=10, pady=5)

        ttk.Button(log_controls, text="Refresh Logs",
                  command=self._refresh_logs).pack(side='left')
        ttk.Button(log_controls, text="Clear Logs",
                  command=self._clear_logs).pack(side='left', padx=(10, 0))

        # Log display
        self.log_text = scrolledtext.ScrolledText(log_frame, height=20)
        self.log_text.pack(fill='both', expand=True, padx=10, pady=5)

        # Auto-refresh logs
        self._refresh_logs()

    def _load_current_config(self):
        """Load current configuration into GUI fields."""
        config = self.app.config_manager

        # Load API key
        self.api_key_var.set(config.get('alpha_vantage.api_key', ''))

        # Load email settings
        self.email_vars['smtp_server_var'].set(config.get('email.smtp_server', 'smtp.gmail.com'))
        self.email_vars['smtp_port_var'].set(str(config.get('email.smtp_port', 587)))
        self.email_vars['sender_email_var'].set(config.get('email.sender_email', ''))
        self.email_vars['sender_password_var'].set(config.get('email.sender_password', ''))
        self.email_vars['recipient_email_var'].set(config.get('email.recipient_email', ''))

        # Load analysis settings
        self.threshold_var.set(config.get('analysis.volume_threshold', 2.0))
        self.days_var.set(config.get('analysis.days_to_average', 5))

        # Load watchlist
        self._load_watchlist()

    def _load_watchlist(self):
        """Load watchlist from file into text widget."""
        try:
            stocks_file = self.app.config_manager.get('watchlist.stocks_file', 'stocks.txt')
            if os.path.exists(stocks_file):
                with open(stocks_file, 'r') as f:
                    content = f.read()
                self.watchlist_text.delete('1.0', tk.END)
                self.watchlist_text.insert('1.0', content)
            else:
                # Load default stocks
                default_stocks = self.app.config_manager.get('watchlist.default_stocks', [])
                self.watchlist_text.delete('1.0', tk.END)
                self.watchlist_text.insert('1.0', '\n'.join(default_stocks))
        except Exception as e:
            self.logger.error(f"Error loading watchlist: {e}")

    # API and Email testing methods
    def _test_api(self):
        """Test Finnhub API connection."""
        try:
            # Save current API key to config
            api_key = self.api_key_var.get().strip()
            if not api_key:
                messagebox.showerror("Error", "Please enter an API key first")
                return

            self.app.config_manager.set('alpha_vantage.api_key', api_key)

            # Test screener (includes API connection)
            success = self.app.volume_screener.test_screener()
            message = "Screener test successful" if success else "Screener test failed"

            if success:
                messagebox.showinfo("API Test", f"✅ {message}")
            else:
                messagebox.showerror("API Test Failed", f"❌ {message}")

        except Exception as e:
            messagebox.showerror("Error", f"API test error: {e}")

    def _test_email(self):
        """Test email configuration."""
        try:
            # Save current email settings to config
            self._save_email_config()

            # Test email connection
            success, message = self.app.email_notifier.send_test_email()

            if success:
                messagebox.showinfo("Email Test", f"✅ {message}")
            else:
                messagebox.showerror("Email Test Failed", f"❌ {message}")

        except Exception as e:
            messagebox.showerror("Error", f"Email test error: {e}")

    def _save_config(self):
        """Save current GUI settings to configuration."""
        try:
            # Save API configuration
            self.app.config_manager.set('alpha_vantage.api_key', self.api_key_var.get().strip())

            # Save email configuration
            self._save_email_config()

            # Save analysis settings
            self.app.config_manager.set('analysis.volume_threshold', self.threshold_var.get())
            self.app.config_manager.set('analysis.days_to_average', self.days_var.get())

            # Save watchlist
            self._save_watchlist()

            # Save configuration file
            if self.app.config_manager.save_config():
                messagebox.showinfo("Success", "Configuration saved successfully!")
                self.logger.info("Configuration saved by user")
            else:
                messagebox.showerror("Error", "Failed to save configuration")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to save configuration: {e}")

    def _save_email_config(self):
        """Save email configuration from GUI to config manager."""
        self.app.config_manager.set('email.smtp_server', self.email_vars['smtp_server_var'].get().strip())
        self.app.config_manager.set('email.smtp_port', int(self.email_vars['smtp_port_var'].get() or 587))
        self.app.config_manager.set('email.sender_email', self.email_vars['sender_email_var'].get().strip())
        self.app.config_manager.set('email.sender_password', self.email_vars['sender_password_var'].get().strip())
        self.app.config_manager.set('email.recipient_email', self.email_vars['recipient_email_var'].get().strip())

    def _load_defaults(self):
        """Load default configuration values."""
        try:
            # Reset to default configuration
            default_config = self.app.config_manager._get_default_config()
            self.app.config_manager.config = default_config

            # Reload GUI with defaults
            self._load_current_config()

            messagebox.showinfo("Success", "Default configuration loaded!")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to load defaults: {e}")

    def _load_watchlist_file(self):
        """Load watchlist from file."""
        try:
            filename = filedialog.askopenfilename(
                title="Load Watchlist",
                filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
            )

            if filename:
                with open(filename, 'r', encoding='utf-8') as f:
                    content = f.read()

                self.watchlist_text.delete('1.0', tk.END)
                self.watchlist_text.insert('1.0', content)

                messagebox.showinfo("Success", f"Watchlist loaded from {filename}")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to load watchlist: {e}")

    def _save_watchlist_file(self):
        """Save watchlist to file."""
        try:
            filename = filedialog.asksaveasfilename(
                title="Save Watchlist",
                defaultextension=".txt",
                filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
            )

            if filename:
                content = self.watchlist_text.get('1.0', tk.END).strip()

                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(content)

                messagebox.showinfo("Success", f"Watchlist saved to {filename}")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to save watchlist: {e}")

    def _save_watchlist(self):
        """Save current watchlist to the default stocks file."""
        try:
            content = self.watchlist_text.get('1.0', tk.END).strip()
            stocks_file = self.app.config_manager.get('watchlist.stocks_file', 'stocks.txt')

            with open(stocks_file, 'w', encoding='utf-8') as f:
                f.write(content)

        except Exception as e:
            self.logger.error(f"Error saving watchlist: {e}")

    def _validate_symbols(self):
        """Validate stock symbols."""
        try:
            content = self.watchlist_text.get('1.0', tk.END).strip()
            symbols = [line.strip().upper() for line in content.split('\n') if line.strip()]

            if not symbols:
                messagebox.showwarning("Warning", "No symbols found in watchlist")
                return

            # Basic validation
            valid_symbols = []
            invalid_symbols = []

            for symbol in symbols:
                # Basic symbol validation (letters only, 1-5 characters)
                if symbol.isalpha() and 1 <= len(symbol) <= 5:
                    valid_symbols.append(symbol)
                else:
                    invalid_symbols.append(symbol)

            message = f"Validation Results:\n\n"
            message += f"✅ Valid symbols: {len(valid_symbols)}\n"

            if invalid_symbols:
                message += f"❌ Invalid symbols: {len(invalid_symbols)}\n"
                message += f"Invalid: {', '.join(invalid_symbols[:10])}"
                if len(invalid_symbols) > 10:
                    message += f" and {len(invalid_symbols) - 10} more..."

            messagebox.showinfo("Symbol Validation", message)

        except Exception as e:
            messagebox.showerror("Error", f"Validation error: {e}")

    def _start_scan(self):
        """Start volume scanning."""
        try:
            # Validate configuration
            if not self.app.config_manager.is_configured():
                missing = self.app.config_manager.get_missing_config()
                messagebox.showerror("Configuration Error",
                                   f"Missing configuration:\n• {chr(10).join(missing)}")
                return

            # Note: New screener doesn't use watchlist - it finds top volume stocks automatically
            # But we'll keep this for user information
            content = self.watchlist_text.get('1.0', tk.END).strip()

            # Show info about the new approach
            response = messagebox.askyesno("Volume Screening",
                                         "This will scan MarketWatch for the most active after-hours stocks "
                                         "and find the top 5 with highest volume ratios.\n\n"
                                         "✅ NO API KEY REQUIRED - Pure MarketWatch scraping!\n"
                                         "⏱️ This will take 2-3 minutes (much faster now).\n\n"
                                         "Continue?")
            if not response:
                return

            # Disable scan button and enable stop button
            self.scan_button.config(state='disabled')
            self.stop_button.config(state='normal')

            # Clear previous results
            self.results_text.delete('1.0', tk.END)
            self.progress_var.set(0)

            # Start scan in separate thread (no symbols needed for new screener)
            self.scan_thread = threading.Thread(target=self._run_scan)
            self.scan_thread.daemon = True
            self.scan_thread.start()

        except Exception as e:
            messagebox.showerror("Error", f"Failed to start scan: {e}")
            self._reset_scan_buttons()

    def _run_scan(self):
        """Run the volume screening in a separate thread."""
        try:
            def progress_callback(progress, status):
                self.root.after(0, lambda: self._update_progress(progress, status))

            # Perform the screening
            results = self.app.perform_volume_scan()

            # Update GUI with results
            self.root.after(0, lambda: self._display_results(results))

        except Exception as e:
            self.root.after(0, lambda: self._scan_error(str(e)))
        finally:
            self.root.after(0, self._reset_scan_buttons)

    def _update_progress(self, progress, status):
        """Update progress bar and status."""
        self.progress_var.set(progress)
        self.status_var.set(status)

    def _display_results(self, results):
        """Display screening results in the GUI."""
        try:
            self.results_text.delete('1.0', tk.END)

            # Display summary
            summary = results.get('summary', {})
            text = f"VOLUME SCREENING RESULTS - {summary.get('analysis_time', '')[:19]}\n"
            text += "=" * 60 + "\n\n"
            text += f"Scraped from MarketWatch: {summary.get('total_scraped', 0)}\n"
            text += f"Successfully Analyzed: {summary.get('successful_analyses', 0)}\n"
            text += f"Failed: {len(results.get('failed', []))}\n"
            text += f"Days Averaged: {summary.get('days_averaged', 2)}\n\n"

            # Display top volume leaders
            if results.get('top_stocks'):
                text += f"🏆 TOP {len(results['top_stocks'])} VOLUME LEADERS:\n"
                text += "-" * 40 + "\n"
                for i, stock in enumerate(results['top_stocks']):
                    alert_icon = "🚨" if stock.get('alert_triggered', False) else "📊"
                    text += f"{alert_icon} {i+1}. {stock['symbol']}: {stock['volume_ratio']:.2f}x\n"
                    text += f"    Current Volume: {stock['current_volume']:,}\n"
                    text += f"    Average Volume: {stock['average_volume']:,.0f}\n"
                    if stock.get('scraped_price'):
                        text += f"    Price: ${stock['scraped_price']:.2f} "
                        text += f"({stock.get('scraped_change_pct', 0):+.2f}%)\n"
                    if stock.get('company_name'):
                        text += f"    Company: {stock['company_name']}\n"
                    text += "\n"

            # Display alerts (stocks above threshold)
            if results.get('alerts'):
                text += f"� VOLUME ALERTS (Above {self.app.config_manager.get('analysis.volume_threshold', 2.0)}x):\n"
                text += "-" * 40 + "\n"
                for alert in results['alerts']:
                    text += f"{alert['symbol']}: {alert['volume_ratio']:.2f}x "
                    text += f"(Current: {alert['current_volume']:,}, "
                    text += f"Avg: {alert['average_volume']:,.0f})\n"
                text += "\n"

            # Display failed stocks
            if results.get('failed'):
                text += "❌ FAILED TO ANALYZE:\n"
                text += "-" * 30 + "\n"
                text += ", ".join(results['failed']) + "\n\n"

            text += "---\n"
            text += "Data: MarketWatch After-Hours Only (100% scraping)\n"
            text += "Volume ratios estimated based on after-hours activity levels\n"
            text += "Generated automatically by Volume Alert App"

            self.results_text.insert('1.0', text)

            alert_count = len(results.get('alerts', []))
            top_count = len(results.get('top_stocks', []))
            self.status_var.set(f"Screening complete - {top_count} top stocks, {alert_count} alerts")

        except Exception as e:
            self.logger.error(f"Error displaying results: {e}")
            self.results_text.insert('1.0', f"Error displaying results: {e}")

    def _scan_error(self, error_msg):
        """Handle scan error."""
        self.results_text.delete('1.0', tk.END)
        self.results_text.insert('1.0', f"Scan Error: {error_msg}")
        self.status_var.set("Scan failed")

    def _stop_scan(self):
        """Stop current scan."""
        # Note: This is a simplified stop - in a production app you'd want
        # proper thread cancellation
        self.status_var.set("Stopping scan...")
        self._reset_scan_buttons()

    def _reset_scan_buttons(self):
        """Reset scan button states."""
        self.scan_button.config(state='normal')
        self.stop_button.config(state='disabled')
        self.progress_var.set(0)

    def _refresh_logs(self):
        """Refresh log display."""
        try:
            log_file = self.app.config_manager.get('logging.log_file', 'scan_log.txt')
            if os.path.exists(log_file):
                with open(log_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                self.log_text.delete('1.0', tk.END)
                self.log_text.insert('1.0', content)
                self.log_text.see(tk.END)
        except Exception as e:
            self.logger.error(f"Error refreshing logs: {e}")

    def _clear_logs(self):
        """Clear log file and display."""
        try:
            log_file = self.app.config_manager.get('logging.log_file', 'scan_log.txt')
            with open(log_file, 'w') as f:
                f.write('')
            self.log_text.delete('1.0', tk.END)
            self.logger.info("Logs cleared by user")
        except Exception as e:
            self.logger.error(f"Error clearing logs: {e}")

    def _on_closing(self):
        """Handle window closing event."""
        self.root.destroy()
