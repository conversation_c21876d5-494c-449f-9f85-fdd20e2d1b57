#!/usr/bin/env python3
"""
After-Hours Volume Alert Desktop App
====================================

A Python desktop application that monitors after-hours trading volume
and sends email alerts when volume exceeds specified thresholds.

Author: AI Assistant
Version: 1.0
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import json
import os
import sys
import logging
import argparse
from datetime import datetime, timedelta
import threading
from typing import Dict, List, Optional, Tuple

# Import our custom modules (will be created)
from config_manager import ConfigManager
from volume_screener import VolumeScreener
from email_notifier import EmailNotifier
from gui_components import VolumeAlertGUI

# Configure logging
def setup_logging():
    """Setup logging configuration for the application."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('scan_log.txt', encoding='utf-8'),
            logging.StreamHandler()
        ]
    )
    return logging.getLogger(__name__)

class VolumeAlertApp:
    """Main application class that coordinates all components."""

    def __init__(self, auto_mode=False):
        """
        Initialize the Volume Alert Application.

        Args:
            auto_mode (bool): If True, run in headless mode for scheduled tasks
        """
        self.logger = setup_logging()
        self.auto_mode = auto_mode

        # Initialize components
        self.config_manager = ConfigManager()
        self.volume_screener = VolumeScreener(self.config_manager)
        self.email_notifier = EmailNotifier(self.config_manager)

        if not auto_mode:
            # Initialize GUI
            self.root = tk.Tk()
            self.gui = VolumeAlertGUI(self.root, self)
            self.logger.info("Application initialized in GUI mode")
        else:
            self.logger.info("Application initialized in auto mode")

    def run(self):
        """Run the application in appropriate mode."""
        if self.auto_mode:
            self.run_auto_scan()
        else:
            self.run_gui()

    def run_gui(self):
        """Run the GUI version of the application."""
        try:
            self.root.mainloop()
        except Exception as e:
            self.logger.error(f"GUI error: {e}")
            messagebox.showerror("Error", f"Application error: {e}")

    def run_auto_scan(self):
        """Run automated scan without GUI (for scheduled tasks)."""
        try:
            self.logger.info("Starting automated scan")
            results = self.perform_volume_scan()

            if results['alerts']:
                self.send_alert_email(results)
                self.logger.info(f"Alert email sent for {len(results['alerts'])} stocks")
            else:
                self.logger.info("No volume alerts triggered")

        except Exception as e:
            self.logger.error(f"Auto scan error: {e}")

    def perform_volume_scan(self) -> Dict:
        """
        Perform volume screening to find top after-hours volume stocks.

        Returns:
            Dict: Results containing top volume stocks and analysis data
        """
        try:
            # Get configuration
            top_n = self.config_manager.get('analysis.top_stocks', 5)
            max_analyze = self.config_manager.get('analysis.max_stocks_to_analyze', 20)

            # Perform volume screening
            results = self.volume_screener.screen_top_volume_stocks(
                top_n=top_n,
                max_stocks_to_analyze=max_analyze
            )

            # Convert to old format for compatibility with email system
            converted_results = {
                'alerts': [],
                'analyzed': results.get('all_analyzed', []),
                'failed': results.get('failed', []),
                'summary': results.get('summary', {}),
                'top_stocks': results.get('top_stocks', []),
                'scraping_data': results.get('scraping_data', [])
            }

            # Mark top stocks as alerts for email notification
            threshold = self.config_manager.get('analysis.volume_threshold', 2.0)
            for stock in results.get('top_stocks', []):
                if stock['volume_ratio'] >= threshold:
                    stock['alert_triggered'] = True
                    converted_results['alerts'].append(stock)

            self.logger.info(f"Volume screening completed: {len(converted_results['top_stocks'])} top stocks found, "
                           f"{len(converted_results['alerts'])} above threshold")

            return converted_results

        except Exception as e:
            self.logger.error(f"Error performing volume scan: {e}")
            return {'alerts': [], 'analyzed': [], 'failed': [], 'summary': {}, 'top_stocks': []}

    def send_alert_email(self, results: Dict):
        """
        Send email alert with volume analysis results.

        Args:
            results (Dict): Scan results containing alert data
        """
        try:
            success, message = self.email_notifier.send_alert_email(results)
            if success:
                self.logger.info(f"Alert email sent: {message}")
            else:
                self.logger.error(f"Failed to send alert email: {message}")
        except Exception as e:
            self.logger.error(f"Error sending alert email: {e}")

    def load_watchlist(self) -> List[str]:
        """
        Load stock symbols from watchlist file.

        Returns:
            List[str]: List of stock symbols
        """
        try:
            stocks_file = self.config_manager.get('watchlist.stocks_file', 'stocks.txt')

            if os.path.exists(stocks_file):
                with open(stocks_file, 'r', encoding='utf-8') as f:
                    symbols = [line.strip().upper() for line in f if line.strip()]
                    symbols = [s for s in symbols if s and not s.startswith('#')]  # Filter comments
                    return symbols
            else:
                # Return default stocks
                return self.config_manager.get('watchlist.default_stocks', [])

        except Exception as e:
            self.logger.error(f"Error loading watchlist: {e}")
            return self.config_manager.get('watchlist.default_stocks', [])

def main():
    """Main entry point for the application."""
    parser = argparse.ArgumentParser(description='After-Hours Volume Alert App')
    parser.add_argument('--auto', action='store_true',
                       help='Run in automated mode (no GUI)')

    args = parser.parse_args()

    try:
        app = VolumeAlertApp(auto_mode=args.auto)
        app.run()
    except KeyboardInterrupt:
        print("\nApplication interrupted by user")
    except Exception as e:
        print(f"Fatal error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
