"""
MarketWatch After-Hours Scraper Module
=====================================

Scrapes MarketWatch after-hours most active stocks to get a pre-filtered
list of high-volume stocks for analysis.
"""

import requests
import logging
from bs4 import BeautifulSoup
from typing import List, Dict, Optional
import time

class MarketWatchScraper:
    """Scrapes MarketWatch for after-hours most active stocks."""
    
    def __init__(self):
        """Initialize the scraper."""
        self.logger = logging.getLogger(__name__)
        self.session = requests.Session()
        
        # Set up session headers to mimic a real browser
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        })
    
    def scrape_after_hours_most_active(self) -> List[Dict]:
        """
        Scrape MarketWatch after-hours most active stocks.
        
        Returns:
            List[Dict]: List of stock data with symbol, volume, price, change
        """
        try:
            url = "https://www.marketwatch.com/tools/screener/after-hours"
            
            self.logger.info(f"Scraping MarketWatch after-hours data from {url}")
            
            response = self.session.get(url, timeout=30)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Find the "Most Active" section
            most_active_stocks = []
            
            # Look for the Most Active table
            # The structure is: table with rows containing stock data
            tables = soup.find_all('table')
            
            for table in tables:
                # Check if this is the Most Active table by looking for volume data
                rows = table.find_all('tr')
                
                if len(rows) < 2:  # Skip tables with no data rows
                    continue
                
                # Check header row to identify Most Active table
                header_row = rows[0]
                headers = [th.get_text().strip().lower() for th in header_row.find_all(['th', 'td'])]
                
                # Look for volume column to identify the right table
                if 'volume' not in ' '.join(headers):
                    continue
                
                self.logger.info(f"Found table with headers: {headers}")
                
                # Process data rows
                for row in rows[1:]:  # Skip header row
                    cells = row.find_all(['td', 'th'])
                    
                    if len(cells) < 4:  # Need at least symbol, price, volume, change
                        continue
                    
                    try:
                        # Extract data from cells
                        # Typical structure: Symbol, Company Name, Price, Volume, Change, Change %
                        symbol_cell = cells[0]
                        symbol_link = symbol_cell.find('a')
                        
                        if symbol_link:
                            symbol = symbol_link.get_text().strip()
                        else:
                            symbol = symbol_cell.get_text().strip()
                        
                        # Skip if symbol is empty or looks like a header
                        if not symbol or symbol.lower() in ['symbol', 'company', 'name']:
                            continue
                        
                        # Extract other data
                        company_name = cells[1].get_text().strip() if len(cells) > 1 else ""
                        price_text = cells[2].get_text().strip() if len(cells) > 2 else "0"
                        volume_text = cells[3].get_text().strip() if len(cells) > 3 else "0"
                        change_text = cells[4].get_text().strip() if len(cells) > 4 else "0"
                        change_pct_text = cells[5].get_text().strip() if len(cells) > 5 else "0"
                        
                        # Clean and parse volume (handle formats like "5.92M", "1.11K")
                        volume = self._parse_volume(volume_text)
                        
                        # Clean and parse price
                        price = self._parse_price(price_text)
                        
                        # Clean and parse change
                        change = self._parse_change(change_text)
                        change_pct = self._parse_change(change_pct_text.replace('%', ''))
                        
                        stock_data = {
                            'symbol': symbol.upper(),
                            'company_name': company_name,
                            'price': price,
                            'volume': volume,
                            'change': change,
                            'change_pct': change_pct,
                            'source': 'MarketWatch After Hours'
                        }
                        
                        most_active_stocks.append(stock_data)
                        
                        self.logger.debug(f"Scraped: {symbol} - Volume: {volume:,}, Price: ${price:.2f}")
                        
                    except Exception as e:
                        self.logger.warning(f"Error parsing row: {e}")
                        continue
                
                # If we found stocks in this table, we're done
                if most_active_stocks:
                    break
            
            # Sort by volume (highest first) and limit to top 20
            most_active_stocks.sort(key=lambda x: x['volume'], reverse=True)
            top_stocks = most_active_stocks[:20]
            
            self.logger.info(f"Successfully scraped {len(top_stocks)} after-hours most active stocks")
            
            return top_stocks
            
        except requests.exceptions.RequestException as e:
            self.logger.error(f"Network error scraping MarketWatch: {e}")
            return []
        except Exception as e:
            self.logger.error(f"Error scraping MarketWatch: {e}")
            return []
    
    def _parse_volume(self, volume_text: str) -> int:
        """
        Parse volume text like "5.92M", "1.11K" into integer.
        
        Args:
            volume_text (str): Volume text from webpage
            
        Returns:
            int: Volume as integer
        """
        try:
            volume_text = volume_text.replace(',', '').strip()
            
            if 'M' in volume_text.upper():
                # Millions
                number = float(volume_text.upper().replace('M', ''))
                return int(number * 1_000_000)
            elif 'K' in volume_text.upper():
                # Thousands
                number = float(volume_text.upper().replace('K', ''))
                return int(number * 1_000)
            else:
                # Regular number
                return int(float(volume_text))
                
        except (ValueError, TypeError):
            return 0
    
    def _parse_price(self, price_text: str) -> float:
        """
        Parse price text like "$195.80" into float.
        
        Args:
            price_text (str): Price text from webpage
            
        Returns:
            float: Price as float
        """
        try:
            # Remove $ and commas
            clean_price = price_text.replace('$', '').replace(',', '').strip()
            return float(clean_price)
        except (ValueError, TypeError):
            return 0.0
    
    def _parse_change(self, change_text: str) -> float:
        """
        Parse change text like "+1.38", "-0.68" into float.
        
        Args:
            change_text (str): Change text from webpage
            
        Returns:
            float: Change as float
        """
        try:
            # Remove extra characters and parse
            clean_change = change_text.replace('+', '').replace('$', '').replace(',', '').strip()
            return float(clean_change)
        except (ValueError, TypeError):
            return 0.0
    
    def get_top_symbols(self, limit: int = 20) -> List[str]:
        """
        Get list of top after-hours most active stock symbols.
        
        Args:
            limit (int): Maximum number of symbols to return
            
        Returns:
            List[str]: List of stock symbols
        """
        stocks = self.scrape_after_hours_most_active()
        return [stock['symbol'] for stock in stocks[:limit]]
    
    def test_scraper(self) -> bool:
        """
        Test the scraper functionality.
        
        Returns:
            bool: True if scraper works, False otherwise
        """
        try:
            stocks = self.scrape_after_hours_most_active()
            
            if not stocks:
                self.logger.error("Scraper test failed: No stocks found")
                return False
            
            self.logger.info(f"Scraper test successful: Found {len(stocks)} stocks")
            
            # Log first few stocks for verification
            for i, stock in enumerate(stocks[:5]):
                self.logger.info(f"  {i+1}. {stock['symbol']}: {stock['volume']:,} volume")
            
            return True
            
        except Exception as e:
            self.logger.error(f"Scraper test failed: {e}")
            return False
