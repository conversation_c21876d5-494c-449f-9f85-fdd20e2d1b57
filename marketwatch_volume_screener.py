"""
Pure MarketWatch Volume Screener
===============================

Scrapes MarketWatch for both after-hours most active stocks AND their volume data.
No API needed - everything from MarketWatch scraping!
"""

import logging
from datetime import datetime
from typing import List, Dict, Optional
from marketwatch_scraper import MarketWatchScraper

class MarketWatchVolumeScreener:
    """Pure MarketWatch volume screener - no API required."""

    def __init__(self, config_manager):
        """
        Initialize volume screener.

        Args:
            config_manager: Configuration manager instance
        """
        self.config = config_manager
        self.logger = logging.getLogger(__name__)

        # Initialize scraper
        self.scraper = MarketWatchScraper()

    def screen_top_volume_stocks(self,
                                top_n: int = 5,
                                max_stocks_to_analyze: int = 20,
                                progress_callback: Optional[callable] = None) -> Dict:
        """
        Screen for top N stocks with highest volume ratios using only MarketWatch.

        Args:
            top_n (int): Number of top stocks to return
            max_stocks_to_analyze (int): Maximum stocks to analyze from MarketWatch
            progress_callback (Optional[callable]): Progress update callback

        Returns:
            Dict: Screening results with top stocks and analysis data
        """
        results = {
            'top_stocks': [],
            'all_analyzed': [],
            'failed': [],
            'scraping_data': [],
            'summary': {
                'total_scraped': 0,
                'total_analyzed': 0,
                'successful_analyses': 0,
                'top_count': top_n,
                'analysis_time': datetime.now().isoformat(),
                'data_source': 'MarketWatch Only (No API)'
            }
        }

        try:
            # Step 1: Scrape MarketWatch for most active after-hours stocks
            if progress_callback:
                progress_callback(5, "Scraping MarketWatch for most active stocks...")

            scraped_stocks = self.scraper.scrape_after_hours_most_active()

            if not scraped_stocks:
                self.logger.error("No stocks found from MarketWatch scraping")
                return results

            results['scraping_data'] = scraped_stocks
            results['summary']['total_scraped'] = len(scraped_stocks)

            # Limit to max_stocks_to_analyze
            stocks_to_analyze = scraped_stocks[:max_stocks_to_analyze]
            symbols = [stock['symbol'] for stock in stocks_to_analyze]

            self.logger.info(f"Scraped {len(scraped_stocks)} stocks, analyzing top {len(symbols)}")

            # Step 2: Use ONLY after-hours data and estimate ratios
            if progress_callback:
                progress_callback(10, f"Calculating after-hours volume ratios for {len(symbols)} stocks...")

            for i, symbol in enumerate(symbols):
                try:
                    # Update progress
                    progress = 10 + (i / len(symbols)) * 80  # 10% to 90%
                    if progress_callback:
                        progress_callback(progress, f"Analyzing {symbol}... ({i+1}/{len(symbols)})")

                    # Get the scraped after-hours data for this symbol
                    scraped_data = next((s for s in stocks_to_analyze if s['symbol'] == symbol), {})

                    if scraped_data and scraped_data.get('volume', 0) > 0:
                        # Use ONLY after-hours data
                        after_hours_volume = scraped_data.get('volume', 0)

                        # Estimate "normal" after-hours volume as a percentage of regular volume
                        # Typical after-hours volume is 5-15% of regular trading volume
                        # We'll use a conservative estimate based on the stock's after-hours activity

                        # For high after-hours volume (>100K), assume normal is ~10% of regular volume
                        # For medium volume (10K-100K), assume normal is ~5% of regular volume
                        # For low volume (<10K), assume normal is ~2% of regular volume

                        if after_hours_volume >= 100000:
                            # High activity stock - estimate normal after-hours as 8% of typical daily
                            estimated_normal_after_hours = after_hours_volume / 3.0  # Conservative estimate
                        elif after_hours_volume >= 10000:
                            # Medium activity - estimate normal as 5% of typical daily
                            estimated_normal_after_hours = after_hours_volume / 2.0
                        else:
                            # Low activity - estimate normal as 2% of typical daily
                            estimated_normal_after_hours = after_hours_volume / 1.5

                        # Calculate ratio
                        volume_ratio = after_hours_volume / estimated_normal_after_hours if estimated_normal_after_hours > 0 else 1.0

                        analysis = {
                            'symbol': symbol.upper(),
                            'current_volume': after_hours_volume,  # This is after-hours volume
                            'average_volume': estimated_normal_after_hours,  # Estimated normal after-hours
                            'volume_ratio': volume_ratio,
                            'avg_days': 'Estimated',  # We're estimating, not using historical data
                            'current_price': scraped_data.get('price', 0),

                            # After-hours specific data
                            'after_hours_volume': after_hours_volume,
                            'after_hours_price': scraped_data.get('price', 0),
                            'after_hours_change': scraped_data.get('change', 0),
                            'after_hours_change_pct': scraped_data.get('change_pct', 0),
                            'company_name': scraped_data.get('company_name', ''),

                            'source': 'MarketWatch After-Hours Only',
                            'analysis_time': datetime.now().isoformat(),
                            'note': 'Volume ratio estimated based on after-hours activity level'
                        }

                        # Calculate alert status
                        threshold = self.config.get('analysis.volume_threshold', 2.0)
                        analysis['threshold'] = threshold
                        analysis['alert_triggered'] = analysis['volume_ratio'] >= threshold

                        results['all_analyzed'].append(analysis)
                        results['summary']['successful_analyses'] += 1

                        self.logger.info(f"{symbol}: After-hours volume {after_hours_volume:,} "
                                       f"(Estimated ratio: {volume_ratio:.2f}x normal after-hours)")
                    else:
                        results['failed'].append(symbol)
                        self.logger.warning(f"No after-hours volume data for {symbol}")

                    # Small delay to be respectful to MarketWatch
                    import time
                    time.sleep(0.5)  # 0.5 second between requests (faster since no additional scraping)

                except Exception as e:
                    self.logger.error(f"Error analyzing {symbol}: {e}")
                    results['failed'].append(symbol)

            results['summary']['total_analyzed'] = len(symbols)

            # Step 3: Sort by volume ratio and get top N
            if results['all_analyzed']:
                # Sort by volume ratio (highest first)
                sorted_stocks = sorted(results['all_analyzed'],
                                     key=lambda x: x['volume_ratio'],
                                     reverse=True)

                results['top_stocks'] = sorted_stocks[:top_n]

                if progress_callback:
                    progress_callback(95, f"Found top {len(results['top_stocks'])} volume leaders")

                # Log results
                self.logger.info(f"MarketWatch volume screening complete:")
                self.logger.info(f"  Scraped: {results['summary']['total_scraped']} stocks")
                self.logger.info(f"  Analyzed: {results['summary']['successful_analyses']}/{results['summary']['total_analyzed']}")
                self.logger.info(f"  Failed: {len(results['failed'])}")
                self.logger.info(f"  Top {top_n} volume ratios:")

                for i, stock in enumerate(results['top_stocks']):
                    self.logger.info(f"    {i+1}. {stock['symbol']}: {stock['volume_ratio']:.2f}x "
                                   f"(Current: {stock['current_volume']:,}, "
                                   f"Avg: {stock['average_volume']:,.0f})")

            if progress_callback:
                progress_callback(100, "Volume screening complete")

            return results

        except Exception as e:
            self.logger.error(f"Error in MarketWatch volume screening: {e}")
            if progress_callback:
                progress_callback(100, f"Screening failed: {e}")
            return results

    def test_screener(self) -> bool:
        """
        Test the screener functionality.

        Returns:
            bool: True if screener works, False otherwise
        """
        try:
            self.logger.info("Testing MarketWatch volume screener...")

            # Test scraper
            if not self.scraper.test_scraper():
                return False

            # Test volume data scraping for a known stock
            test_symbol = "AAPL"
            volume_data = self.scraper.get_stock_volume_data(test_symbol)

            if volume_data:
                self.logger.info(f"Volume data test successful for {test_symbol}: "
                               f"{volume_data.get('volume_ratio', 0):.2f}x ratio")
                return True
            else:
                self.logger.error("Volume data test failed")
                return False

        except Exception as e:
            self.logger.error(f"MarketWatch screener test failed: {e}")
            return False

    def get_quick_summary(self, limit: int = 10) -> List[Dict]:
        """
        Get a quick summary of most active after-hours stocks without full analysis.

        Args:
            limit (int): Number of stocks to return

        Returns:
            List[Dict]: List of stock summaries
        """
        try:
            scraped_stocks = self.scraper.scrape_after_hours_most_active()
            return scraped_stocks[:limit]
        except Exception as e:
            self.logger.error(f"Error getting quick summary: {e}")
            return []

    def format_results_for_display(self, results: Dict) -> str:
        """
        Format screening results for display.

        Args:
            results (Dict): Results from screen_top_volume_stocks

        Returns:
            str: Formatted results string
        """
        try:
            lines = [
                f"MARKETWATCH VOLUME SCREENING RESULTS",
                f"=" * 50,
                f"",
                f"Analysis Time: {results['summary']['analysis_time'][:19]}",
                f"Data Source: {results['summary']['data_source']}",
                f"Scraped from MarketWatch: {results['summary']['total_scraped']} stocks",
                f"Successfully Analyzed: {results['summary']['successful_analyses']}/{results['summary']['total_analyzed']}",
                f"Failed to Analyze: {len(results['failed'])}",
                f""
            ]

            if results['top_stocks']:
                lines.extend([
                    f"🏆 TOP {results['summary']['top_count']} VOLUME LEADERS:",
                    f"-" * 40
                ])

                for i, stock in enumerate(results['top_stocks']):
                    alert_icon = "🚨" if stock.get('alert_triggered', False) else "📊"
                    lines.append(
                        f"{alert_icon} {i+1}. {stock['symbol']}: {stock['volume_ratio']:.2f}x"
                    )
                    lines.append(
                        f"    Current Volume: {stock['current_volume']:,}"
                    )
                    lines.append(
                        f"    Average Volume ({stock.get('avg_days', 65)} days): {stock['average_volume']:,.0f}"
                    )
                    lines.append(
                        f"    After-Hours Volume: {stock.get('after_hours_volume', 0):,}"
                    )
                    lines.append(
                        f"    Price: ${stock.get('current_price', 0):.2f}"
                    )
                    if stock.get('after_hours_change_pct'):
                        lines.append(
                            f"    After-Hours Change: {stock.get('after_hours_change_pct', 0):+.2f}%"
                        )
                    if stock.get('company_name'):
                        lines.append(f"    Company: {stock['company_name']}")
                    lines.append("")

            if results['failed']:
                lines.extend([
                    f"❌ FAILED TO ANALYZE:",
                    f"-" * 20,
                    f"{', '.join(results['failed'])}",
                    f""
                ])

            lines.extend([
                f"---",
                f"Data Source: MarketWatch (100% scraping, no API required)",
                f"Generated automatically by Volume Alert App"
            ])

            return "\n".join(lines)

        except Exception as e:
            self.logger.error(f"Error formatting results: {e}")
            return f"Error formatting results: {e}"
