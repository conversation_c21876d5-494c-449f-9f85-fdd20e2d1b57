"""
Simple After-Hours Screener
===========================

Gets the top 5 stocks with biggest positive after-hours price changes from MarketWatch.
NO fake calculations, NO API, NO complexity.
"""

import requests
import logging
import re
from bs4 import BeautifulSoup
from typing import List, Dict
from datetime import datetime

class SimpleAfterHoursScreener:
    """Simple screener for biggest after-hours gainers."""
    
    def __init__(self):
        """Initialize the screener."""
        self.logger = logging.getLogger(__name__)
        self.session = requests.Session()
        
        # Set up session headers
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Connection': 'keep-alive'
        })
    
    def get_top_after_hours_gainers(self, top_n: int = 5) -> List[Dict]:
        """
        Get top N stocks with biggest positive after-hours changes.
        
        Args:
            top_n (int): Number of top gainers to return
            
        Returns:
            List[Dict]: List of top gainers with stock data
        """
        try:
            url = "https://www.marketwatch.com/tools/screener/after-hours"
            
            self.logger.info(f"Scraping after-hours gainers from {url}")
            
            response = self.session.get(url, timeout=30)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Find all tables
            tables = soup.find_all('table')
            all_stocks = []
            
            for table in tables:
                rows = table.find_all('tr')
                
                if len(rows) < 2:
                    continue
                
                # Check header row to identify table type
                header_row = rows[0]
                headers = [th.get_text().strip().lower() for th in header_row.find_all(['th', 'td'])]
                
                # Look for tables with volume, price, change data
                if not any(keyword in ' '.join(headers) for keyword in ['volume', 'chg', 'price']):
                    continue
                
                self.logger.info(f"Processing table with headers: {headers}")
                
                # Process data rows
                for row in rows[1:]:
                    cells = row.find_all(['td', 'th'])
                    
                    if len(cells) < 5:
                        continue
                    
                    try:
                        # Extract symbol
                        symbol_cell = cells[0]
                        symbol_link = symbol_cell.find('a')
                        
                        if symbol_link:
                            symbol = symbol_link.get_text().strip()
                        else:
                            symbol = symbol_cell.get_text().strip()
                        
                        if not symbol or symbol.lower() in ['symbol', 'company', 'name']:
                            continue
                        
                        # Extract data
                        company_name = cells[1].get_text().strip() if len(cells) > 1 else ""
                        price_text = cells[2].get_text().strip() if len(cells) > 2 else "0"
                        volume_text = cells[3].get_text().strip() if len(cells) > 3 else "0"
                        change_text = cells[4].get_text().strip() if len(cells) > 4 else "0"
                        change_pct_text = cells[5].get_text().strip() if len(cells) > 5 else "0"
                        
                        # Parse data
                        price = self._parse_price(price_text)
                        volume = self._parse_volume(volume_text)
                        change = self._parse_change(change_text)
                        change_pct = self._parse_change_pct(change_pct_text)
                        
                        # Only include stocks with POSITIVE changes
                        if change > 0 and change_pct > 0:
                            stock_data = {
                                'symbol': symbol.upper(),
                                'company_name': company_name,
                                'price': price,
                                'volume': volume,
                                'change': change,
                                'change_pct': change_pct,
                                'analysis_time': datetime.now().isoformat()
                            }
                            
                            all_stocks.append(stock_data)
                            
                            self.logger.info(f"Found gainer: {symbol} +{change_pct:.2f}% (${change:+.2f})")
                        
                    except Exception as e:
                        self.logger.warning(f"Error parsing row: {e}")
                        continue
            
            # Sort by percentage change (highest first) and return top N
            gainers = sorted(all_stocks, key=lambda x: x['change_pct'], reverse=True)
            top_gainers = gainers[:top_n]
            
            self.logger.info(f"Found {len(top_gainers)} top after-hours gainers")
            
            return top_gainers
            
        except Exception as e:
            self.logger.error(f"Error scraping after-hours gainers: {e}")
            return []
    
    def _parse_volume(self, volume_text: str) -> int:
        """Parse volume text like '5.92M', '1.11K' into integer."""
        try:
            volume_text = volume_text.replace(',', '').strip()
            
            if 'M' in volume_text.upper():
                number = float(volume_text.upper().replace('M', ''))
                return int(number * 1_000_000)
            elif 'K' in volume_text.upper():
                number = float(volume_text.upper().replace('K', ''))
                return int(number * 1_000)
            else:
                return int(float(volume_text))
                
        except (ValueError, TypeError):
            return 0
    
    def _parse_price(self, price_text: str) -> float:
        """Parse price text like '$195.80' into float."""
        try:
            clean_price = price_text.replace('$', '').replace(',', '').strip()
            return float(clean_price)
        except (ValueError, TypeError):
            return 0.0
    
    def _parse_change(self, change_text: str) -> float:
        """Parse change text like '+1.38', '-0.68' into float."""
        try:
            clean_change = change_text.replace('+', '').replace('$', '').replace(',', '').strip()
            return float(clean_change)
        except (ValueError, TypeError):
            return 0.0
    
    def _parse_change_pct(self, change_pct_text: str) -> float:
        """Parse change percentage like '6.12%' into float."""
        try:
            clean_pct = change_pct_text.replace('%', '').replace('+', '').strip()
            return float(clean_pct)
        except (ValueError, TypeError):
            return 0.0
    
    def format_results(self, gainers: List[Dict]) -> str:
        """Format results for display."""
        if not gainers:
            return "No after-hours gainers found."
        
        lines = [
            "🚀 TOP AFTER-HOURS GAINERS",
            "=" * 40,
            f"Analysis Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            ""
        ]
        
        for i, stock in enumerate(gainers):
            lines.append(f"🔥 {i+1}. {stock['symbol']}: +{stock['change_pct']:.2f}%")
            lines.append(f"    Price: ${stock['price']:.2f} (+${stock['change']:.2f})")
            lines.append(f"    Volume: {stock['volume']:,}")
            if stock['company_name']:
                lines.append(f"    Company: {stock['company_name']}")
            lines.append("")
        
        lines.extend([
            "---",
            "Data: MarketWatch After-Hours (100% scraping)",
            "Showing stocks with positive after-hours changes only"
        ])
        
        return "\n".join(lines)
    
    def test_screener(self) -> bool:
        """Test the screener."""
        try:
            gainers = self.get_top_after_hours_gainers(3)
            
            if gainers:
                self.logger.info(f"Test successful: Found {len(gainers)} gainers")
                for gainer in gainers:
                    self.logger.info(f"  {gainer['symbol']}: +{gainer['change_pct']:.2f}%")
                return True
            else:
                self.logger.error("Test failed: No gainers found")
                return False
                
        except Exception as e:
            self.logger.error(f"Test failed: {e}")
            return False
