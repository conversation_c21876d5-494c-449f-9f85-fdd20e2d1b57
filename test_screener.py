#!/usr/bin/env python3
"""Test the fixed screener"""

from marketwatch_volume_screener import MarketWatchVolumeScreener
from config_manager import ConfigManager
import logging

logging.basicConfig(level=logging.INFO)

config = ConfigManager()
screener = MarketWatchVolumeScreener(config)

print('=== TESTING FIXED SCREENER ===')
results = screener.screen_top_volume_stocks(top_n=3, max_stocks_to_analyze=5)

print('Top stocks:')
for i, stock in enumerate(results['top_stocks']):
    symbol = stock['symbol']
    ratio = stock['volume_ratio']
    current = stock['current_volume']
    avg = stock['average_volume']
    company = stock['company_name']
    
    print(f'{i+1}. {symbol}: {ratio:.2f}x')
    print(f'   After-hours volume: {current:,}')
    print(f'   Estimated normal: {avg:,.0f}')
    print(f'   Company: {company}')
    print()
