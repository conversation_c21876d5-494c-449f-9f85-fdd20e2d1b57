"""
Volume Analyzer Module
=====================

Handles stock volume analysis using Finnhub API.
Compares after-hours volume against historical averages.
"""

import requests
import logging
import time
from datetime import datetime, timedelta, timezone
from typing import Dict, List, Optional, Tuple
import json

class VolumeAnalyzer:
    """Analyzes stock volume using Finnhub API."""
    
    def __init__(self, config_manager):
        """
        Initialize volume analyzer.
        
        Args:
            config_manager: Configuration manager instance
        """
        self.config = config_manager
        self.logger = logging.getLogger(__name__)
        self.session = requests.Session()
        
        # Set up session headers
        self.session.headers.update({
            'User-Agent': 'VolumeAlertApp/1.0'
        })
    
    def _get_api_key(self) -> str:
        """
        Get Finnhub API key from configuration.
        
        Returns:
            str: API key
            
        Raises:
            ValueError: If API key is not configured
        """
        api_key = self.config.get('finnhub.api_key')
        if not api_key:
            raise ValueError("Finnhub API key not configured")
        return api_key
    
    def _get_base_url(self) -> str:
        """Get Finnhub base URL from configuration."""
        return self.config.get('finnhub.base_url', 'https://finnhub.io/api/v1')
    
    def _make_api_request(self, endpoint: str, params: Dict) -> Optional[Dict]:
        """
        Make API request to Finnhub.
        
        Args:
            endpoint (str): API endpoint
            params (Dict): Request parameters
            
        Returns:
            Optional[Dict]: API response data or None if failed
        """
        try:
            # Add API key to parameters
            params['token'] = self._get_api_key()
            
            url = f"{self._get_base_url()}/{endpoint}"
            
            self.logger.debug(f"Making API request to {url} with params: {params}")
            
            response = self.session.get(url, params=params, timeout=30)
            response.raise_for_status()
            
            data = response.json()
            
            # Check for API errors
            if 'error' in data:
                self.logger.error(f"API error: {data['error']}")
                return None
            
            return data
            
        except requests.exceptions.RequestException as e:
            self.logger.error(f"API request failed: {e}")
            return None
        except json.JSONDecodeError as e:
            self.logger.error(f"Failed to parse API response: {e}")
            return None
    
    def test_api_connection(self) -> Tuple[bool, str]:
        """
        Test API connection and key validity.
        
        Returns:
            Tuple[bool, str]: (success, message)
        """
        try:
            # Test with a simple quote request
            data = self._make_api_request('quote', {'symbol': 'AAPL'})
            
            if data is None:
                return False, "Failed to connect to Finnhub API"
            
            if 'c' not in data:  # 'c' is current price
                return False, "Invalid API response format"
            
            return True, "API connection successful"
            
        except Exception as e:
            return False, f"API test failed: {e}"
    
    def _get_trading_timestamps(self, days_back: int = 0) -> Tuple[int, int]:
        """
        Get after-hours trading timestamps for a specific day.
        
        Args:
            days_back (int): Number of days back from today (0 = today)
            
        Returns:
            Tuple[int, int]: (start_timestamp, end_timestamp) for after-hours
        """
        # Get target date
        target_date = datetime.now() - timedelta(days=days_back)
        
        # After-hours: 8:00 PM previous day to 9:29 AM current day (EST)
        # Note: This is a simplified approach - in production, you'd want to
        # handle market holidays and weekends properly
        
        # Start: 8:00 PM of the previous trading day
        start_time = target_date.replace(hour=20, minute=0, second=0, microsecond=0)
        start_time -= timedelta(days=1)  # Previous day
        
        # End: 9:29 AM of the target day
        end_time = target_date.replace(hour=9, minute=29, second=0, microsecond=0)
        
        # Convert to UTC timestamps (Finnhub expects UTC)
        # Note: This assumes EST timezone - in production, use proper timezone handling
        start_timestamp = int(start_time.timestamp())
        end_timestamp = int(end_time.timestamp())
        
        return start_timestamp, end_timestamp
    
    def get_after_hours_volume(self, symbol: str, days_back: int = 0) -> Optional[int]:
        """
        Get after-hours volume for a specific stock and day.
        
        Args:
            symbol (str): Stock symbol
            days_back (int): Number of days back from today
            
        Returns:
            Optional[int]: Total after-hours volume or None if failed
        """
        try:
            start_ts, end_ts = self._get_trading_timestamps(days_back)
            
            params = {
                'symbol': symbol.upper(),
                'resolution': '5',  # 5-minute intervals
                'from': start_ts,
                'to': end_ts,
                'extended_hours': 'true'  # Include extended hours data
            }
            
            data = self._make_api_request('stock/candle', params)
            
            if data is None or data.get('s') != 'ok':
                self.logger.warning(f"No data available for {symbol} on day -{days_back}")
                return None
            
            # Sum up all volume data points
            volumes = data.get('v', [])
            if not volumes:
                return None
            
            total_volume = sum(volumes)
            
            self.logger.debug(f"{symbol} after-hours volume (day -{days_back}): {total_volume:,}")
            
            return total_volume
            
        except Exception as e:
            self.logger.error(f"Error getting after-hours volume for {symbol}: {e}")
            return None
    
    def calculate_average_volume(self, symbol: str, days: int) -> Optional[float]:
        """
        Calculate average after-hours volume over specified days.
        
        Args:
            symbol (str): Stock symbol
            days (int): Number of days to average over
            
        Returns:
            Optional[float]: Average volume or None if insufficient data
        """
        volumes = []
        
        for day_back in range(1, days + 1):  # Start from 1 day back
            volume = self.get_after_hours_volume(symbol, day_back)
            if volume is not None:
                volumes.append(volume)
            
            # Add small delay to avoid rate limiting
            time.sleep(0.1)
        
        if not volumes:
            self.logger.warning(f"No volume data available for {symbol}")
            return None
        
        if len(volumes) < days * 0.6:  # Require at least 60% of requested days
            self.logger.warning(f"Insufficient data for {symbol}: {len(volumes)}/{days} days")
            return None
        
        average = sum(volumes) / len(volumes)
        self.logger.debug(f"{symbol} average volume over {len(volumes)} days: {average:,.0f}")
        
        return average
    
    def analyze_stock(self, symbol: str) -> Optional[Dict]:
        """
        Analyze a single stock for volume alerts.
        
        Args:
            symbol (str): Stock symbol to analyze
            
        Returns:
            Optional[Dict]: Analysis results or None if failed
        """
        try:
            # Get configuration
            threshold = self.config.get('analysis.volume_threshold', 2.0)
            days_to_average = self.config.get('analysis.days_to_average', 5)
            
            # Get today's after-hours volume
            current_volume = self.get_after_hours_volume(symbol, 0)
            if current_volume is None:
                return None
            
            # Get average volume
            avg_volume = self.calculate_average_volume(symbol, days_to_average)
            if avg_volume is None:
                return None
            
            # Calculate ratio
            volume_ratio = current_volume / avg_volume if avg_volume > 0 else 0
            
            # Determine if alert should be triggered
            alert_triggered = volume_ratio >= threshold
            
            result = {
                'symbol': symbol.upper(),
                'current_volume': current_volume,
                'average_volume': avg_volume,
                'volume_ratio': volume_ratio,
                'threshold': threshold,
                'alert_triggered': alert_triggered,
                'analysis_time': datetime.now().isoformat()
            }
            
            self.logger.info(f"{symbol}: Volume ratio {volume_ratio:.2f}x "
                           f"(current: {current_volume:,}, avg: {avg_volume:,.0f}) "
                           f"- Alert: {alert_triggered}")
            
            return result
            
        except Exception as e:
            self.logger.error(f"Error analyzing {symbol}: {e}")
            return None
    
    def analyze_watchlist(self, symbols: List[str], 
                         progress_callback: Optional[callable] = None) -> Dict:
        """
        Analyze multiple stocks from watchlist.
        
        Args:
            symbols (List[str]): List of stock symbols
            progress_callback (Optional[callable]): Progress update callback
            
        Returns:
            Dict: Analysis results with alerts and summary
        """
        results = {
            'alerts': [],
            'analyzed': [],
            'failed': [],
            'summary': {
                'total_symbols': len(symbols),
                'successful_analyses': 0,
                'alerts_triggered': 0,
                'analysis_time': datetime.now().isoformat()
            }
        }
        
        for i, symbol in enumerate(symbols):
            try:
                # Update progress
                if progress_callback:
                    progress = (i / len(symbols)) * 100
                    progress_callback(progress, f"Analyzing {symbol}...")
                
                # Analyze stock
                analysis = self.analyze_stock(symbol.strip().upper())
                
                if analysis:
                    results['analyzed'].append(analysis)
                    results['summary']['successful_analyses'] += 1
                    
                    if analysis['alert_triggered']:
                        results['alerts'].append(analysis)
                        results['summary']['alerts_triggered'] += 1
                else:
                    results['failed'].append(symbol)
                
                # Rate limiting delay
                time.sleep(0.2)
                
            except Exception as e:
                self.logger.error(f"Error in watchlist analysis for {symbol}: {e}")
                results['failed'].append(symbol)
        
        # Final progress update
        if progress_callback:
            progress_callback(100, "Analysis complete")
        
        self.logger.info(f"Watchlist analysis complete: "
                        f"{results['summary']['successful_analyses']}/{len(symbols)} analyzed, "
                        f"{results['summary']['alerts_triggered']} alerts")
        
        return results
