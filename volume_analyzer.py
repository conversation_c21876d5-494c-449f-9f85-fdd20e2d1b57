"""
Volume Analyzer Module
=====================

Handles stock volume analysis using Finnhub API.
Compares after-hours volume against historical averages.
"""

import requests
import logging
import time
from datetime import datetime, timedelta, timezone
from typing import Dict, List, Optional, Tuple
import json

class VolumeAnalyzer:
    """Analyzes stock volume using Finnhub API."""

    def __init__(self, config_manager):
        """
        Initialize volume analyzer.

        Args:
            config_manager: Configuration manager instance
        """
        self.config = config_manager
        self.logger = logging.getLogger(__name__)
        self.session = requests.Session()

        # Set up session headers
        self.session.headers.update({
            'User-Agent': 'VolumeAlertApp/1.0'
        })

    def _get_api_key(self) -> str:
        """
        Get Finnhub API key from configuration.

        Returns:
            str: API key

        Raises:
            ValueError: If API key is not configured
        """
        api_key = self.config.get('finnhub.api_key')
        if not api_key:
            raise ValueError("Finnhub API key not configured")
        return api_key

    def _get_base_url(self) -> str:
        """Get Finnhub base URL from configuration."""
        return self.config.get('finnhub.base_url', 'https://finnhub.io/api/v1')

    def _make_api_request(self, endpoint: str, params: Dict) -> Optional[Dict]:
        """
        Make API request to Finnhub.

        Args:
            endpoint (str): API endpoint
            params (Dict): Request parameters

        Returns:
            Optional[Dict]: API response data or None if failed
        """
        try:
            # Add API key to parameters
            params['token'] = self._get_api_key()

            url = f"{self._get_base_url()}/{endpoint}"

            self.logger.debug(f"Making API request to {url} with params: {params}")

            response = self.session.get(url, params=params, timeout=30)
            response.raise_for_status()

            data = response.json()

            # Check for API errors
            if 'error' in data:
                self.logger.error(f"API error: {data['error']}")
                return None

            return data

        except requests.exceptions.RequestException as e:
            self.logger.error(f"API request failed: {e}")
            return None
        except json.JSONDecodeError as e:
            self.logger.error(f"Failed to parse API response: {e}")
            return None

    def test_api_connection(self) -> Tuple[bool, str]:
        """
        Test API connection and key validity.

        Returns:
            Tuple[bool, str]: (success, message)
        """
        try:
            # Test with a simple quote request
            data = self._make_api_request('quote', {'symbol': 'AAPL'})

            if data is None:
                return False, "Failed to connect to Finnhub API"

            if 'c' not in data:  # 'c' is current price
                return False, "Invalid API response format"

            return True, "API connection successful"

        except Exception as e:
            return False, f"API test failed: {e}"

    def _get_trading_timestamps(self, days_back: int = 0) -> Tuple[int, int]:
        """
        Get trading timestamps for a specific day.

        Args:
            days_back (int): Number of days back from today (0 = today)

        Returns:
            Tuple[int, int]: (start_timestamp, end_timestamp) for the trading day
        """
        # Get target date
        target_date = datetime.now() - timedelta(days=days_back)

        # For daily data, we just need the day range
        # Start of day
        start_time = target_date.replace(hour=0, minute=0, second=0, microsecond=0)

        # End of day
        end_time = target_date.replace(hour=23, minute=59, second=59, microsecond=0)

        # Convert to UTC timestamps
        start_timestamp = int(start_time.timestamp())
        end_timestamp = int(end_time.timestamp())

        self.logger.debug(f"Timestamps for day -{days_back}: {start_timestamp} to {end_timestamp}")

        return start_timestamp, end_timestamp

    def get_daily_volume(self, symbol: str, days_back: int = 0) -> Optional[int]:
        """
        Get daily trading volume for a specific stock and day.

        Args:
            symbol (str): Stock symbol
            days_back (int): Number of days back from today

        Returns:
            Optional[int]: Total daily volume or None if failed
        """
        try:
            start_ts, end_ts = self._get_trading_timestamps(days_back)

            params = {
                'symbol': symbol.upper(),
                'resolution': 'D',  # Daily data instead of 5-minute for now
                'from': start_ts,
                'to': end_ts
                # Removed extended_hours for free tier compatibility
            }

            data = self._make_api_request('stock/candle', params)

            if data is None or data.get('s') != 'ok':
                self.logger.warning(f"No data available for {symbol} on day -{days_back}")
                return None

            # Get volume data (for daily data, it's just one data point per day)
            volumes = data.get('v', [])
            if not volumes:
                return None

            # For daily data, take the volume for that day
            daily_volume = volumes[0] if len(volumes) > 0 else 0

            self.logger.debug(f"{symbol} daily volume (day -{days_back}): {daily_volume:,}")

            return daily_volume

        except Exception as e:
            self.logger.error(f"Error getting after-hours volume for {symbol}: {e}")
            return None

    def calculate_average_volume(self, symbol: str, days: int) -> Optional[float]:
        """
        Calculate average after-hours volume over specified days.

        Args:
            symbol (str): Stock symbol
            days (int): Number of days to average over

        Returns:
            Optional[float]: Average volume or None if insufficient data
        """
        volumes = []

        for day_back in range(1, days + 1):  # Start from 1 day back
            volume = self.get_daily_volume(symbol, day_back)
            if volume is not None:
                volumes.append(volume)

            # Add small delay to avoid rate limiting
            time.sleep(0.1)

        if not volumes:
            self.logger.warning(f"No volume data available for {symbol}")
            return None

        if len(volumes) < days * 0.6:  # Require at least 60% of requested days
            self.logger.warning(f"Insufficient data for {symbol}: {len(volumes)}/{days} days")
            return None

        average = sum(volumes) / len(volumes)
        self.logger.debug(f"{symbol} average volume over {len(volumes)} days: {average:,.0f}")

        return average

    def analyze_stock(self, symbol: str) -> Optional[Dict]:
        """
        Analyze a single stock for volume alerts.

        Args:
            symbol (str): Stock symbol to analyze

        Returns:
            Optional[Dict]: Analysis results or None if failed
        """
        try:
            # Get configuration
            threshold = self.config.get('analysis.volume_threshold', 2.0)
            days_to_average = self.config.get('analysis.days_to_average', 5)

            # Get today's daily volume
            current_volume = self.get_daily_volume(symbol, 0)
            if current_volume is None:
                return None

            # Get average volume
            avg_volume = self.calculate_average_volume(symbol, days_to_average)
            if avg_volume is None:
                return None

            # Calculate ratio
            volume_ratio = current_volume / avg_volume if avg_volume > 0 else 0

            # Determine if alert should be triggered
            alert_triggered = volume_ratio >= threshold

            result = {
                'symbol': symbol.upper(),
                'current_volume': current_volume,
                'average_volume': avg_volume,
                'volume_ratio': volume_ratio,
                'threshold': threshold,
                'alert_triggered': alert_triggered,
                'analysis_time': datetime.now().isoformat()
            }

            self.logger.info(f"{symbol}: Volume ratio {volume_ratio:.2f}x "
                           f"(current: {current_volume:,}, avg: {avg_volume:,.0f}) "
                           f"- Alert: {alert_triggered}")

            return result

        except Exception as e:
            self.logger.error(f"Error analyzing {symbol}: {e}")
            return None

    def analyze_watchlist(self, symbols: List[str],
                         progress_callback: Optional[callable] = None) -> Dict:
        """
        Analyze multiple stocks from watchlist.

        Args:
            symbols (List[str]): List of stock symbols
            progress_callback (Optional[callable]): Progress update callback

        Returns:
            Dict: Analysis results with alerts and summary
        """
        results = {
            'alerts': [],
            'analyzed': [],
            'failed': [],
            'summary': {
                'total_symbols': len(symbols),
                'successful_analyses': 0,
                'alerts_triggered': 0,
                'analysis_time': datetime.now().isoformat()
            }
        }

        for i, symbol in enumerate(symbols):
            try:
                # Update progress
                if progress_callback:
                    progress = (i / len(symbols)) * 100
                    progress_callback(progress, f"Analyzing {symbol}...")

                # Analyze stock
                analysis = self.analyze_stock(symbol.strip().upper())

                if analysis:
                    results['analyzed'].append(analysis)
                    results['summary']['successful_analyses'] += 1

                    if analysis['alert_triggered']:
                        results['alerts'].append(analysis)
                        results['summary']['alerts_triggered'] += 1
                else:
                    results['failed'].append(symbol)

                # Rate limiting delay
                time.sleep(0.2)

            except Exception as e:
                self.logger.error(f"Error in watchlist analysis for {symbol}: {e}")
                results['failed'].append(symbol)

        # Final progress update
        if progress_callback:
            progress_callback(100, "Analysis complete")

        self.logger.info(f"Watchlist analysis complete: "
                        f"{results['summary']['successful_analyses']}/{len(symbols)} analyzed, "
                        f"{results['summary']['alerts_triggered']} alerts")

        return results
